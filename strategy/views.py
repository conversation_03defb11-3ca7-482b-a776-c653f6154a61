from collections import defaultdict
from os import X_OK
from django.shortcuts import render
from django.db.models import Q
from functools import reduce
import operator
from django.http import JsonResponse
from datetime import datetime
import numpy as np
from tools import ctptrade,tdxdata,loaddata


# Create your views here.
from .models import Product,Info,System,Value
import pandas as pd
import json

def index(request):
    dataset = System.objects.all()
    
    return render(request, 'index.html', {"dataset":dataset})  # 返回模板页面

def updatedata(request,arg):
    marketmap = {"DCE":29,"CZCE":28,"SHFE":30,"CFFEX":47,"GFEX":66,"INE":30}
    ignorelist = ["bb","fb", "PM","RI","RS", "WH","JR","wr","LR","rr","ZC"]
    daily = "D"
    fifteen = "15"
    zs = "L9"
    zl = "L8"
    dataset = Info.objects.all()
    tdx = tdxdata()
    errorcontract = []
    for data in dataset:
        if data.name in ignorelist:continue #废弃品种跳过
        
        market = marketmap[data.exchangeid]
        if data.istrade:
            if arg == "contract":
                for contract in data.contractlist:
                    if contract[-4] != "2" :  #部分合约后面只有三个数字，倒数第四个位置插入2，待变2000年
                        contract = contract[:-3]+"2"+contract[-3:]  
                    try:
                        tdx.update(data.name.upper(),contract.upper(),market,fifteen) #更新15分钟数据
                        tdx.update(data.name.upper(),contract.upper(),market,daily)   #更新日线数据
                    except:
                        errorcontract.append(contract)
                        print("!!!!!!!!!! get data error for {}".format(contract))
            elif arg == "main":
                tdx.update(data.name.upper()+zs,data.name.upper()+zs,market,fifteen)
                tdx.update(data.name.upper()+zs,data.name.upper()+zs,market,daily)
                tdx.update(data.name.upper()+zl,data.name.upper()+zl,market,fifteen)
                tdx.update(data.name.upper()+zl,data.name.upper()+zl,market,daily)
            
    return JsonResponse({'status': 'finished', 'message': '成功',"errorcontract":errorcontract})

def updateconfig(request):
    '''更新升贴水信息
    '''
    # if request.method == 'POST':
    #     ctd = ctptrade()       
    #     ctd.updateinstrument()
    ctd = ctptrade()
    ctd.updateinstrument()
    productlist = list(ctd.td_spi.pricetick.keys())
    prolst = productlist
    
    contractlist = {k:[] for k in productlist}
    for contract,name in ctd.td_spi.productlist.items():
        contractlist[name].append(contract)
    
    dataset = Info.objects.all()
    for data in dataset:
        if data.name in productlist:
            data.pricetick = ctd.td_spi.pricetick[data.name]
            data.exchangeid = ctd.td_spi.exchangeid[data.name]
            data.contractlist = contractlist[data.name]
            data.istrade = True
            productlist.remove(data.name)
        else:
            data.istrade = False
            
        data.save()
    
    for name in productlist:
        Info.objects.create(name=name,
                            pricetick = ctd.td_spi.pricetick[name],
                            exchangeid = ctd.td_spi.exchangeid[name],
                            contractlist = contractlist[name]
                            ) 

    return JsonResponse({'status': 'finished', 'message': '成功',"productlist":prolst})
    
def quotations(request):
    ctd = loaddata()
    data = {}
    contractmap = {}
    result = {}
    dfs = []
    keys = []
    for collection in ctd.mongo.list_collection_names():
        if "L9_D" in collection:
            # dataset[collection] = ctd.getdata(collection) #获取所有品种指数数据
            df = pd.DataFrame(list(ctd.getdata(collection,projection={"date":1,"close":1,"_id":0}))[::-1])
            df.loc[:,"close"] = ((df["close"]-df["close"].shift(1))/df["close"].shift(1)).cumsum().round(3)
            dfs.append(df.set_index('date').rename(columns={'close': collection}))
            keys.append(collection)
            
        elif "_D" in collection and 'L8_D' not in collection :
            p = collection[:-4]
            if contractmap.get(p):
                contractmap[p].append(collection)
            else: contractmap[p] = [collection]
            data[collection] = ctd.getdata(collection,limit=1)
    
    
    fmat = "%Y-%m-%d %H:%M"
    merged = pd.concat(dfs, axis=1).fillna(method="ffill").fillna(0)
    datelst = merged.index.strftime(fmat).values
    data_list = merged[keys].to_dict("list")
    
    for k,contractlist in sorted(contractmap.items(), key=lambda x: x[0]):
        temp = []
        for contract in contractlist:
            dt = list(data[contract])
            temp.append([contract,dt[-1]["position"],int(contract[-4:-2]),dt[-1]["close"],dt[-1]["date"].strftime(fmat)])
        temp.sort(key=lambda x: x[1],reverse=True)
        divmon = temp[0][2]-temp[1][2] #主力合约减去此主力
        wht = 1 if divmon >0 else -1
        result[k] = {"main":temp[0][0][:-2],"mainclose":round(temp[0][3],3),
                     "secmain":temp[1][0][:-2],"secmainclose":round(temp[1][3],3),
                     "tiehsui":round((temp[0][3]-temp[1][3])*wht,3),
                     "tieshuibaifenbi":round((temp[0][3]-temp[1][3])*wht/temp[0][3]*100,2),
                     "rate":round((temp[0][3]-temp[1][3])*wht/temp[0][3]*12/abs(divmon)*100,2),
                     "date":temp[0][4]}

    return render(request, 'quotations.html', {"result":result,"datelst":datelst,"keys":keys,"data_list":data_list})

def quotationsdetail(request,name):
    name = name.upper()
    ctd = loaddata()
    dfs = []
    keys = []
    for collection in ctd.mongo.list_collection_names():
        if collection[:-4] == name and "_D" in collection:
            df = pd.DataFrame(list(ctd.getdata(collection,projection={"date":1,"close":1,"_id":0}))[::-1])
            dfs.append(df.set_index('date').rename(columns={'close': collection}))
            keys.append(collection)
    
    fmat = "%Y-%m-%d %H:%M"
    merged = pd.concat(dfs, axis=1).fillna(method="ffill").fillna(0)
    datelst = merged.index.strftime(fmat).values
    data_list = merged[keys].to_dict("list")
    
    
    return render(request, 'quotationsdetail.html',{"datelst":datelst,"keys":keys,"data_list":data_list})
            


def main(request):
    data = System.objects.all()
    th = ["组合名字","组合品种列表","组合曲线"]
    # td = [{"name":"组合一","product":"RB:1 HC:1  I:1","link":"link","number":0},{"name":"组合二","product":"RB:2 HC:3  I:3","link":"link","number":1}]
    # print(namelist)
    context = {'message': 'Hello, Django!',"data":data,"th":th}  # 传递上下文数据
    return render(request, 'main.html', context)  # 返回模板页面

def systemshow(request,version,product):
    versionlist = version.split("+")
    name = []
    totaldata = []
    data_new = []
    productlist = []
    colormap = {}
    count = 0
    for version in versionlist:
        colormap[version] = count
        count +=1
        
        data = System.objects.get(name=version)
        args = data.tradelist[product]["args"]
        productlist.extend(data.tradelist.keys())
        argslist = []
        weight = {}
        for i in eval(args):
            if i[-1] <=0:
                argslist.append(str(i[:-1]))
                weight[str(i[:-1])] = i[-1]
            else:
                argslist.append(str(i))
                weight[str(i)] = 1
        data = Product.objects.filter(name=product+"L9_15",args__in=argslist)
        
        
        data_tmp = [[i,weight[i.args],version] for i in data]
        
        for i in data:
            data_new.append([i,weight[i.args],version])
        
        lst = []
        for v,w,_ in data_tmp:
            lst.append([int(d.get("values", 0) * w)  for d in v.value])
            
        totaldata.append(zip(*lst))
        # merged_data = zip(*lst)
        for k,w in weight.items():
            name.append("{}:{}_{}".format(k,w,version))
        # indexnum += 1
        name.append("total_"+version)
    
    x = [i["date"].strftime("%Y-%m-%d") for i in data[0].value]
    
    name_map = {"CBOT.ZC":"美玉米",                 "CBOT.ZW":"美麦",
                  "CME.FC":"育肥牛",                  "CME.LN":"瘦肉猪",
                "NYMEX.NG":"天然气",                 "CBOT.ZF":"5年美债",
                 "CBOT.ZT":"2年美债",                  "CME.AD":"澳元",
                  "CME.BP":"英镑",                  "CME.CD":"加元",
                  "CME.EC":"欧元",                  "CME.SF":"瑞郎",
                 "ICUS.DX":"美元指",                  "CME.JY":"日元",
                 "BMD.CPO":"马棕油",                 "CBOT.ZL":"美豆油",
                 "CBOT.ZM":"美豆粕",                  "CBOT.S":"美豆",
                 "ICUS.CC":"可可",                 "ICUS.CT":"美棉花",
                 "ICUS.OJ":"橙汁",                 "CBOT.YM":"迷你道指",
                  "CME.NQ":"微纳指",                "COMEX.GC":"CMX黄金",
                "COMEX.HG":"CMX铜",                "COMEX.SI":"CMX白银",
                  "ICEU.B":"布伦特",                "NYMEX.RB":"汽油",
                 "CBOT.ZB":"长期美债",                 "CBOT.ZN":"10年美债",}
    
    return render(request, 'system.html', {"data":data_new,"totaldata":zip(totaldata,versionlist),"x":x,"name":name,
                                           "versionlist":versionlist,"productlist":sorted(set(productlist)),
                                           "colormap":colormap,"name_map":name_map})  # 返回模板页面

def total(request,version):
    data = System.objects.get(name=version)
    
    profuctlist = data.tradelist.keys()
    q_objects = []
    weight = {}
    for product in profuctlist:
        args = data.tradelist[product]["args"]
        name = product+"L9_15"
        argslist = []
        weight[name] = {}
        wt = data.tradelist[product]["number"]/len(eval(args))
        for i in eval(args):
            if i[-1] <=0:
                argslist.append(str(i[:-1]))
                weight[name][str(i[:-1])] = i[-1]*wt
            else:
                argslist.append(str(i))
                weight[name][str(i)] = wt
        q_objects.append(Q(name=name, args__in=argslist))
                
    data = Product.objects.filter(reduce(operator.or_, q_objects))
    
    ##主要执行时间在下面部分#################
    result = {} #存放所有品种合并的结果
    resultper = {} #存放每个品种单独的结果
    for i in data:
        name = i.name
        args = i.args
        resultper[name] = resultper.get(name,{})
        for item in i.value:
            dt = item["date"].strftime("%Y-%m-%d")
            result[dt] = result.get(dt,0) + item["values"] * weight[name][args]
            resultper[name][dt] =  resultper[name].get(dt,0) + item["values"] * weight[name][args]
        # print(i)
        
    datelist = sorted(result.keys())
    
    return render(request, 'total.html', {"result":result,"x":datelist,"resultper":resultper,"name":version,"savepage":True})  # 返回模板页面

# @csrf_exempt  # 临时禁用CSRF验证（实际应配置中间件）
# 接收POST请求并保存数据

def savevalue(request):
    if request.method == 'POST':
        result = request.POST.get('result')
        detail = request.POST.get('detail')
        name = request.POST.get('name').strip()
        
        data = eval(result.replace('&#x27;',"'"))
        detail = eval(detail.replace('&#x27;',"'"))
        sorted_data = {k: v for k, v in sorted(data.items(), key=lambda item: item[0])}
        try:
            Value.objects.update_or_create(name=name,defaults={"value":sorted_data,"detail":detail})
            return JsonResponse({'status': 'success', 'message': '数据已保存'})
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})
    
    return JsonResponse({'status': 'error', 'message': '无效请求'})

def product(request,name):
    current_url = request.build_absolute_uri()
    if current_url.split("/")[-1] == "1":
        selectedMode = "single"
    else:
        selectedMode = "multiple"

    # data = Product.objects.all()
    data = Product.objects.filter(name=name)
    # print(data)
    x = [i["date"].strftime("%Y-%m-%d") for i in data[0].value]
    name = [i.args for i in data]
    # print(x)
    # print(data)
    # df = pd.DataFrame(data[0].value)
    # print(df)
    # for i in data:
    #     print(i.name,i.args,i.value)
    
    return render(request, 'product.html', {"data":data,"x":x,"name":name,"selectedMode":selectedMode})  # 返回模板页面

def analyseversion(request,version):
    data = Value.objects.get(name=version)
    resultper = data.detail
    result = data.value
    datelist = data.value.keys()
    
    dataargs = System.objects.get(name=version)
    dataargs.tradelist = dict(sorted(dataargs.tradelist.items(), key=lambda x: x[0]))

    stategy_status = ["多敏感","反空敏感","空敏感","反多敏感","趋势对称","反趋势"]
    name_map = {"CBOT.ZC":"美玉米",                 "CBOT.ZW":"美麦",
                  "CME.FC":"育肥牛",                  "CME.LN":"瘦肉猪",
                "NYMEX.NG":"天然气",                 "CBOT.ZF":"5年美债",
                 "CBOT.ZT":"2年美债",                  "CME.AD":"澳元",
                  "CME.BP":"英镑",                  "CME.CD":"加元",
                  "CME.EC":"欧元",                  "CME.SF":"瑞郎",
                 "ICUS.DX":"美元指",                  "CME.JY":"日元",
                 "BMD.CPO":"马棕油",                 "CBOT.ZL":"美豆油",
                 "CBOT.ZM":"美豆粕",                  "CBOT.S":"美豆",
                 "ICUS.CC":"可可",                 "ICUS.CT":"美棉花",
                 "ICUS.OJ":"橙汁",                 "CBOT.YM":"迷你道指",
                  "CME.NQ":"微纳指",                "COMEX.GC":"CMX黄金",
                "COMEX.HG":"CMX铜",                "COMEX.SI":"CMX白银",
                  "ICEU.B":"布伦特",                "NYMEX.RB":"汽油",
                 "CBOT.ZB":"长期美债",                 "CBOT.ZN":"10年美债",}
    # productlist = data1.tradelist.keys()
    # for k,v in data1.tradelist.items():
        
    
    
    # print(productlist)
    
    return render(request, 'total_per.html', {"result":result,"x":datelist,"resultper":resultper,"name":version,"dataargs":dataargs,"stategy_status":stategy_status,"name_map":name_map})  # 返回模板页面
   
def analyse(request):
    # 设置Pandas选项，避免FutureWarning
    # pd.set_option('future.no_silent_downcasting', True)
    
    dataset = Value.objects.all()
    if not dataset:
        return render(request, 'analyse.html', {})
    
    # 获取URL参数中的开始和结束日期，如果没有提供则使用默认值
    # 格式: ?start_date=2015-01-01&end_date=2026-12-31
    start_date_param = request.GET.get('start_date', None)
    end_date_param = request.GET.get('end_date', None)
    
    # 分析每个版本的日期范围
    version_date_ranges = {}
    all_dates = []
    earliest_start_date = None
    latest_end_date = None
    
    # 循环遍历每个版本，获取其最早和最晚日期
    for data in dataset:
        if data.value:
            version_dates = list(data.value.keys())
            version_start = min(version_dates)
            version_end = max(version_dates)
            
            # 更新该版本的日期范围
            version_date_ranges[data.name] = {
                'start': version_start,
                'end': version_end
            }
            
            # 更新全局最早和最晚日期
            if earliest_start_date is None or version_start < earliest_start_date:
                earliest_start_date = version_start
            if latest_end_date is None or version_end > latest_end_date:
                latest_end_date = version_end
                
            all_dates.extend(version_dates)
        # 如果没有数据，使用默认值
    if not all_dates:
        data_start_date = '2015-01-01'  # 根据图表显示，实际数据从2015年开始
        current_year = datetime.now().year
        data_end_date = f'{current_year + 1}-12-31'
        year_end = data_end_date
    else:
        # 使用找到的全局最早和最晚日期
        data_start_date = earliest_start_date
        data_end_date = latest_end_date
        
        # 不为了显示2013年而强制调整开始日期
        # 根据实际数据确定起始日期
            
        # 计算数据结束日期所在年份的年底
        end_date_obj = datetime.strptime(data_end_date, '%Y-%m-%d')
        year_end = datetime(end_date_obj.year, 12, 31).strftime('%Y-%m-%d')
    
    # 验证并处理开始日期参数
    if start_date_param:
        try:
            # 验证日期格式
            start_date = datetime.strptime(start_date_param, '%Y-%m-%d').strftime('%Y-%m-%d')
        except ValueError:
            # 如果格式无效，使用数据中的最早日期
            start_date = data_start_date
    else:
        # 默认使用数据中的最早日期
        start_date = data_start_date
    
    # 验证并处理结束日期参数
    if end_date_param:
        try:
            # 验证日期格式
            end_date = datetime.strptime(end_date_param, '%Y-%m-%d').strftime('%Y-%m-%d')
        except ValueError:
            # 如果格式无效，使用数据中的最晚日期
            end_date = year_end
    else:
        # 默认使用数据最晚日期所在年份的年底
        end_date = year_end
    
    # 提取开始年份和结束年份，用于过滤和标题显示
    start_year = start_date[:4]
    end_year = end_date[:4]
    
    # 获取所有日期列表并排序，用于生成完整的日期范围
    all_unique_dates = sorted(set(all_dates))
    
    # 过滤出在选定日期范围内的所有日期
    date_range_filtered = [d for d in all_unique_dates if d >= start_date and d <= end_date]
    
    # 如果没有日期范围，创建一个空列表
    if not date_range_filtered:
        date_range_filtered = []
    
    for data in dataset:
        # 获取该版本的起始和结束日期
        if data.name in version_date_ranges:
            version_start = version_date_ranges[data.name]['start']
            version_end = version_date_ranges[data.name]['end']
            
            # 获取该版本的原始数据
            all_version_data = {k: int(round(v)) for k, v in data.value.items()}
            
            # 过滤出该版本的相对收益数据
            # 首先获取起始日期之前的最后一条数据作为基准点
            baseline_value = 0
            baseline_date = None
            
            # 找到起始日期之前的最接近的数据点作为基准
            for k, v in sorted(all_version_data.items()):
                if k < start_date:
                    baseline_value = v
                    baseline_date = k
                else:
                    break
            
            # 根据基准点计算相对收益
            filtered_data = {}
            for k, v in all_version_data.items():
                if k >= start_date and k <= end_date:
                    # 如果有基准点，计算相对值，否则使用原始值
                    if baseline_date:
                        filtered_data[k] = v - baseline_value
                    else:
                        filtered_data[k] = v
            
            if not filtered_data:
                data.value = {}
                continue
            
            # 为所有日期创建完整数据集
            complete_data = {}
            
            # 按日期顺序处理
            prev_value = 0  # 相对收益的初始值为0
            first_data_found = False
            
            # 遍历日期范围内的每一天
            for date in date_range_filtered:
                # 检查当前日期是否有数据
                if date in filtered_data:
                    first_data_found = True
                    complete_data[date] = filtered_data[date]
                    prev_value = filtered_data[date]  # 更新前一天的值
                # 如果已经找到了第一个数据点，但当前日期没有数据，使用前一天的值
                elif first_data_found:
                    complete_data[date] = prev_value
                # 如果还没有找到第一个数据点，设为0
                else:
                    complete_data[date] = 0
            
            # 更新数据值
            data.value = complete_data
        else:
            # 如果没有版本信息，设置为空字典
            data.value = {}

        # 注意: 当版本没有日期信息时已经处理了，物没有其他特殊情况需要处理
        
    # 初始化数据结构
    monthly_stats = {}
    yearly_stats = {}
    yearly_drawdown_stats = {}
    all_time_stats = {}  # 添加所有年份的统计数据
    all_time_drawdown_stats = {}  # 添加所有年份的回撤统计
    all_months = set()
    initial_capital = 10000000  # 初始资金1000万
    
    # 单次循环处理所有数据
    for data in dataset:
        name = data.name
        monthly_stats[name] = {}
        yearly_stats[name] = {}
        yearly_drawdown_stats[name] = {}
        
        # 将数据转换为 DataFrame - 只做一次
        # 优化：直接使用列表推导式创建DataFrame，减少中间步骤
        df = pd.DataFrame([
            {'date': k, 'value': v} 
            for k, v in sorted(data.value.items())
        ])
        
        # 如果没有数据，跳过此策略
        if df.empty:
            continue
            
        df['date'] = pd.to_datetime(df['date'])
        
        # 添加年份和月份列，用于分组
        # 优化：使用更高效的日期属性提取
        df['year'] = df['date'].dt.year.astype(str)
        df['year_month'] = df['date'].dt.strftime('%Y-%m')
        
        # 收集所有月份
        all_months.update(df['year_month'].unique())
        
        # 计算回撤 - 只做一次
        df['peak'] = df['value'].cummax()
        df['drawdown'] = df['value'] - df['peak']
        df['drawdown_pct'] = df['drawdown'] / initial_capital * 100
        
        # 优化：使用更高效的分组聚合操作
        # 月度和年度统计合并处理
        monthly_stats[name], yearly_stats[name] = calculate_period_stats(df, initial_capital)
        
        # 计算年度回撤统计 - 优化为单独函数
        yearly_drawdown_stats[name] = calculate_drawdown_stats(df, initial_capital)
        
        # 计算所有年份的统计数据
        first_value = df['value'].iloc[0]
        last_value = df['value'].iloc[-1]
        # 总收益率 = (最后一天累计收益 - 第一天累计收益) / 1000万 × 100%
        total_return = last_value - first_value
        total_rate = total_return / 10000000 * 100  # 除以1000万
        min_drawdown = df['drawdown'].min()
        ratio = round(total_return / abs(min_drawdown), 2) if min_drawdown < 0 else float('inf')
        
        all_time_stats[name] = {
            'return': int(total_return),
            'rate': round(total_rate, 2),
            'return_drawdown_ratio': ratio if ratio != float('inf') else '-'  # 修改这里，将无穷大替换为'-'
        }
        
        # 计算所有年份的回撤统计
        all_time_drawdown_stats[name] = calculate_all_time_drawdown_stats(df, initial_capital)
    
    # 处理月份分组（只需一次）- 优化为更简洁的代码
    all_months = sorted(all_months)
    months_by_year = {}
    
    # 获取所有年份并按照数字大小排序（从旧到新）
    years = sorted(set(month[:4] for month in all_months), reverse=False)  # 修改为从旧到新排序
    
    # 为每个年份创建完整的月份列表（1-12月）
    for year in years:
        months_by_year[year] = []
        for month in range(1, 13):
            month_str = f"{year}-{month:02d}"
            months_by_year[year].append(month_str)
    
    # 添加"所有年份"选项
    months_by_year['all'] = all_months
    
    # 确保所有策略在所有月份都有数据，如果没有则设为'-'
    for name in monthly_stats:
        for year in months_by_year:
            if year != 'all':
                for month in months_by_year[year]:
                    if month not in monthly_stats[name]:
                        monthly_stats[name][month] = {
                            'return': '-',
                            'rate': '-',
                            'return_drawdown_ratio': '-'
                        }
    
    for name in yearly_stats:
        for year in yearly_stats[name]:
            if 'rate' in yearly_stats[name][year] and (yearly_stats[name][year]['rate'] is None or yearly_stats[name][year]['rate'] == float('inf')):
                yearly_stats[name][year]['rate'] = '-'
            if 'return_drawdown_ratio' in yearly_stats[name][year] and (yearly_stats[name][year]['return_drawdown_ratio'] is None or yearly_stats[name][year]['return_drawdown_ratio'] == float('inf')):
                yearly_stats[name][year]['return_drawdown_ratio'] = '-'
    
    return render(request, 'analyse.html', {
        "dataset": dataset,
        "x": list(dataset[0].value.keys()) if dataset else [],
        "monthly_stats": monthly_stats,
        "all_months": all_months,
        "months_by_year": months_by_year,
        "yearly_stats": yearly_stats,
        "yearly_drawdown_stats": yearly_drawdown_stats,
        "all_time_stats": all_time_stats,
        "all_time_drawdown_stats": all_time_drawdown_stats,
        "start_date": start_date,  # 添加开始日期
        "end_date": end_date,      # 添加结束日期
        "start_year": start_year,  # 开始年份
        "end_year": end_year,      # 结束年份
        "data_start_date": data_start_date,  # 数据中的最早日期
        "data_end_date": data_end_date,      # 数据中的最晚日期
        "year_end": year_end  # 年底日期
    })

# 添加计算所有年份回撤统计的函数
def calculate_all_time_drawdown_stats(df, initial_capital):
    """计算所有年份的回撤统计"""
    # 计算相对收益序列（相对于初始值）
    initial_value = df['value'].iloc[0]
    df_copy = df.copy()
    df_copy['relative_return'] = df_copy['value'] - initial_value
    
    # 计算累计最大值和回撤
    df_copy['peak'] = df_copy['relative_return'].cummax()
    df_copy['drawdown'] = df_copy['relative_return'] - df_copy['peak']
    df_copy['drawdown_pct'] = df_copy['drawdown'] / initial_capital * 100
    
    # 修改回撤区间识别逻辑
    # 当drawdown为0时，表示创新高，此时重新开始一个回撤区间
    df_copy['new_peak'] = (df_copy['drawdown'] == 0)
    df_copy['drawdown_group'] = df_copy['new_peak'].cumsum()
    
    # 收集回撤区间
    drawdown_groups = []
    for group_id, group in df_copy.groupby('drawdown_group'):
        # 只处理包含回撤的组
        if (group['drawdown'] < 0).any():
            # 找到组内的峰值点（第一个点）
            peak_date = group['date'].iloc[0]
            peak_value = group['value'].iloc[0]
            
            # 找到组内的最低点
            min_idx = group['drawdown'].idxmin()
            trough_date = group.loc[min_idx, 'date']
            trough_value = group.loc[min_idx, 'value']
            min_drawdown = group.loc[min_idx, 'drawdown']
            min_drawdown_pct = group.loc[min_idx, 'drawdown_pct']
            
            # 计算回撤持续天数（从峰值到低谷）
            duration_days = (trough_date - peak_date).days + 1
            
            # 只有当回撤值小于0时才添加到回撤组
            if min_drawdown < 0:
                drawdown_groups.append({
                    'min_drawdown': min_drawdown,
                    'min_drawdown_pct': min_drawdown_pct,
                    'start_date': peak_date,
                    'end_date': group['date'].iloc[-1],
                    'peak_date': peak_date,
                    'peak_value': peak_value,
                    'trough_date': trough_date,
                    'trough_value': trough_value,
                    'duration_days': duration_days
                })
    
    # 按回撤绝对值排序
    drawdown_groups.sort(key=lambda x: x['min_drawdown'])
    
    # 初始化回撤统计
    drawdown_stats = {
        'max_drawdown': {'value': 0, 'rate': 0, 'period': '-', 'duration_days': 0, 'peak_value': 0, 'trough_value': 0},
        'second_drawdown': {'value': 0, 'rate': 0, 'period': '-', 'duration_days': 0, 'peak_value': 0, 'trough_value': 0},
        'third_drawdown': {'value': 0, 'rate': 0, 'period': '-', 'duration_days': 0, 'peak_value': 0, 'trough_value': 0}
    }
    
    # 填充回撤数据
    drawdown_keys = ['max_drawdown', 'second_drawdown', 'third_drawdown']
    for i, group in enumerate(drawdown_groups[:3]):
        if i < len(drawdown_keys):
            key = drawdown_keys[i]
            drawdown_stats[key] = {
                'value': int(group['min_drawdown']),
                'rate': round(group['min_drawdown_pct'], 2),
                'period': f"{group['peak_date'].strftime('%Y-%m-%d')} 至 {group['trough_date'].strftime('%Y-%m-%d')}",
                'duration_days': group['duration_days'],
                'peak_value': int(group['peak_value']),
                'trough_value': int(group['trough_value'])
            }
    
    return drawdown_stats

# 抽取计算周期统计的函数
def calculate_period_stats(df, initial_capital):
    """计算月度和年度统计数据"""
    monthly_stats = {}
    yearly_stats = {}
    
    # 按月份分组
    df_by_month = df.groupby('year_month')
    
    # 获取所有月份并排序
    all_months = sorted(df_by_month.groups.keys())
    
    # 先找出每月最后一天的累计收益额
    month_end_value = {}
    for month in all_months:
        month_data = df_by_month.get_group(month)
        month_end_value[month] = month_data['value'].iloc[-1]
    
    # 处理每个月份
    for i, month in enumerate(all_months):
        month_data = df_by_month.get_group(month)
        this_month_end = month_end_value[month]
        
        # 计算月度收益率：(该月最后一天累计收益额 - 上月最后一天累计收益额) / 1000万 × 100%
        if i == 0:
            # 第一个月，直接用该月最后一天累计收益额 / 1000万 × 100%
            monthly_return = this_month_end
        else:
            # 不是第一个月，用该月最后一天 - 上月最后一天
            prev_month = all_months[i - 1]
            prev_month_end = month_end_value[prev_month]
            monthly_return = this_month_end - prev_month_end
        
        monthly_rate = monthly_return / 10000000 * 100  # 除以1000万
        max_drawdown = month_data['drawdown'].min()
        
        # 计算收益回撤比
        ratio = round(monthly_return / abs(max_drawdown), 2) if max_drawdown < 0 else float('inf')
        
        monthly_stats[month] = {
            'return': int(monthly_return),
            'rate': round(monthly_rate, 2),
            'return_drawdown_ratio': ratio
        }
    
    # 年度统计：年度收益率 = (今年年末累计收益 - 去年年末累计收益) / 1000万 × 100%
    df_by_year = df.groupby('year')
    all_years = sorted(df_by_year.groups.keys())
    
    # 先找出每年最后一个有数据的累计收益
    year_end_value = {}
    for year in all_years:
        year_data = df_by_year.get_group(year)
        year_end_value[year] = year_data['value'].iloc[-1]
    
    # 计算年度收益和收益率
    for i, year in enumerate(all_years):
        this_year_end = year_end_value[year]
        
        # 年度收益 = 今年年末累计收益 - 去年年末累计收益
        if i == 0:
            # 第一年，去年年末为0
            yearly_return = this_year_end
        else:
            prev_year = all_years[i - 1]
            prev_year_end = year_end_value[prev_year]
            yearly_return = this_year_end - prev_year_end
            
        yearly_rate = yearly_return / 10000000 * 100  # 除以1000万
        year_data = df_by_year.get_group(year)
        max_drawdown = year_data['drawdown'].min()
        ratio = round(yearly_return / abs(max_drawdown), 2) if max_drawdown < 0 else float('inf')
        yearly_stats[year] = {
            'return': int(yearly_return),
            'rate': round(yearly_rate, 2),
            'return_drawdown_ratio': ratio
        }
    
    return monthly_stats, yearly_stats

# 抽取计算回撤统计的函数
def calculate_drawdown_stats(df, initial_capital):
    """计算年度回撤统计"""
    yearly_drawdown_stats = {}
    
    # 按年份分组处理
    for year, year_data in df.groupby('year'):
        # 计算相对收益序列（相对于年初值）
        initial_value = year_data['value'].iloc[0]
        year_data = year_data.copy()
        year_data['relative_return'] = year_data['value'] - initial_value
        
        # 计算累计最大值和回撤
        year_data['peak'] = year_data['relative_return'].cummax()
        year_data['drawdown'] = year_data['relative_return'] - year_data['peak']
        year_data['drawdown_pct'] = year_data['drawdown'] / initial_capital * 100
        
        # 修改回撤区间识别逻辑
        # 当drawdown为0时，表示创新高，此时重新开始一个回撤区间
        year_data['new_peak'] = (year_data['drawdown'] == 0)
        year_data['drawdown_group'] = year_data['new_peak'].cumsum()
        
        # 收集回撤区间
        drawdown_groups = []
        for group_id, group in year_data.groupby('drawdown_group'):
            # 只处理包含回撤的组
            if (group['drawdown'] < 0).any():
                # 找到组内的峰值点（第一个点）
                peak_date = group['date'].iloc[0]
                peak_value = group['value'].iloc[0]
                
                # 找到组内的最低点
                min_idx = group['drawdown'].idxmin()
                trough_date = group.loc[min_idx, 'date']
                trough_value = group.loc[min_idx, 'value']
                min_drawdown = group.loc[min_idx, 'drawdown']
                min_drawdown_pct = group.loc[min_idx, 'drawdown_pct']
                
                # 计算回撤持续天数（从峰值到低谷）
                duration_days = (trough_date - peak_date).days + 1
                
                # 只有当回撤值小于0时才添加到回撤组
                if min_drawdown < 0:
                    drawdown_groups.append({
                        'min_drawdown': min_drawdown,
                        'min_drawdown_pct': min_drawdown_pct,
                        'start_date': peak_date,
                        'end_date': group['date'].iloc[-1],
                        'peak_date': peak_date,
                        'peak_value': peak_value,
                        'trough_date': trough_date,
                        'trough_value': trough_value,
                        'duration_days': duration_days
                    })
        
        # 按回撤绝对值排序
        drawdown_groups.sort(key=lambda x: x['min_drawdown'])
        
        # 初始化年度回撤统计
        yearly_drawdown_stats[year] = {
            'max_drawdown': {'value': 0, 'rate': 0, 'period': '-', 'duration_days': 0, 'peak_value': 0, 'trough_value': 0},
            'second_drawdown': {'value': 0, 'rate': 0, 'period': '-', 'duration_days': 0, 'peak_value': 0, 'trough_value': 0},
            'third_drawdown': {'value': 0, 'rate': 0, 'period': '-', 'duration_days': 0, 'peak_value': 0, 'trough_value': 0}
        }
        
        # 填充回撤数据
        drawdown_keys = ['max_drawdown', 'second_drawdown', 'third_drawdown']
        for i, group in enumerate(drawdown_groups[:3]):
            if i < len(drawdown_keys):
                key = drawdown_keys[i]
                yearly_drawdown_stats[year][key] = {
                    'value': int(group['min_drawdown']),
                    'rate': round(group['min_drawdown_pct'], 2),
                    'period': f"{group['peak_date'].strftime('%Y-%m-%d')} 至 {group['trough_date'].strftime('%Y-%m-%d')}",
                    'duration_days': group['duration_days'],
                    'peak_value': int(group['peak_value']),
                    'trough_value': int(group['trough_value'])
                }
    
    return yearly_drawdown_stats

def get_version_data(versions):
    """
    一次性从数据库中获取版本数据。
    :param versions: 逗号分隔的版本列表，例如 "v1,v2,v3"
    :return: (版本的value数据字典, 版本的detail数据字典, 版本列表)
    """
    version_list = versions.split(',')
    version_objects = Value.objects.filter(name__in=version_list)
    return (
        {obj.name: obj.value for obj in version_objects},  # 版本名称 -> value
        {obj.name: obj.detail for obj in version_objects},  # 版本名称 -> detail
        version_list  # 分割后的版本列表
    )

def is_valid_date(date_string, format="%Y-%m-%d"):
    """
    检查字符串是否为有效的日期格式。
    :param date_string: 日期字符串，例如 "2020-01-01"
    :param format: 日期格式，默认为"%Y-%m-%d"
    :return: True 如果日期有效，否则 False
    """
    try:
        datetime.strptime(date_string, format)
        return True
    except ValueError:
        return False

def version_compare(request, versions):
    """多版本收益对比视图函数"""
    # 一次性获取所有版本数据
    version_list = versions.split(',')
    version_objects = Value.objects.filter(name__in=version_list)
    all_version_data = {obj.name: obj.value for obj in version_objects}
    all_version_products = {obj.name: obj.detail for obj in version_objects}

    if not all_version_data:
        return HttpResponse("未找到指定版本的数据")

    # 预处理日期
    date_year_map = {}
    valid_dates_by_year = defaultdict(list)
    
    for data in all_version_data.values():
        for date_str in data.keys():
            if date_str not in date_year_map and is_valid_date(date_str):
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                year = date_obj.year
                if year >= 2018:
                    date_year_map[date_str] = year
                    valid_dates_by_year[year].append(date_str)

    # 按年份整理数据
    yearly_data = {}
    annual_data = {}
    
    for year, dates in valid_dates_by_year.items():
        sorted_dates = sorted(dates)
        first_date, last_date = sorted_dates[0], sorted_dates[-1]
        
        # 初始化年度数据结构
        yearly_data[year] = {
            "dates": sorted_dates,
            "values": defaultdict(list),
            "first_date": first_date,
            "last_date": last_date
        }
        
        # 收集各版本在该年的值
        for version in version_list:
            data = all_version_data.get(version, {})
            yearly_data[year]["values"][version] = [float(data.get(dt, 0)) for dt in sorted_dates]
        
        # 计算相对收益曲线
        annual_data[year] = [
            {
                "version": version,
                "values": [v - yearly_data[year]["values"][version][0] for v in yearly_data[year]["values"][version]]
            } 
            for version in version_list if yearly_data[year]["values"][version]
        ]

    # 预处理产品数据
    base_version = version_list[0] if version_list else None
    product_returns_by_year = defaultdict(dict)
    sorted_products_by_year = {}
    
    if base_version and base_version in all_version_products:
        for year in annual_data:
            products = all_version_products[base_version]
            
            # 计算每个产品在该年的收益
            for product, values in products.items():
                if not isinstance(values, dict) or not ("L9_15" in product or product.startswith("SP")):
                    continue
                    
                year_dates = [dt for dt in values if date_year_map.get(dt) == year]
                next_year_dates = [dt for dt in values if date_year_map.get(dt) == year + 1]
                
                if year_dates:
                    first_date = min(year_dates)
                    last_date = min(next_year_dates) if next_year_dates else max(year_dates)
                    product_returns_by_year[year][product] = round(float(values.get(last_date, 0)) - float(values.get(first_date, 0)))
            
            # 按收益排序产品
            sorted_products_by_year[year] = [p for p, _ in sorted(
                product_returns_by_year[year].items(), key=lambda x: x[1], reverse=True
            )]

    # 准备柱状图和五年数据
    bar_data = defaultdict(list)
    five_year_product_data = defaultdict(lambda: defaultdict(int))
    five_year_products = set()
    
    for year in annual_data:
        sorted_products = sorted_products_by_year.get(year, [])
        
        for version in version_list:
            products = all_version_products.get(version, {})
            product_values = []
            
            # 计算每个产品的年度收益
            for product in sorted_products:
                values = products.get(product, {}) if isinstance(products, dict) else {}
                year_dates = [dt for dt in values if date_year_map.get(dt) == year]
                next_year_dates = [dt for dt in values if date_year_map.get(dt) == year + 1]
                
                if year_dates:
                    first_date = min(year_dates)
                    last_date = min(next_year_dates) if next_year_dates else max(year_dates)
                    product_values.append(round(float(values.get(last_date, 0)) - float(values.get(first_date, 0))))
                else:
                    product_values.append(0)
                    
            # 存储柱状图数据
            bar_data[year].append({"version": version, "values": product_values, "products": sorted_products})
            
            # 累计五年收益数据
            if 2020 <= year <= 2025:
                for i, product in enumerate(sorted_products):
                    five_year_product_data[product][version] += product_values[i]
                five_year_products.update(sorted_products)

    # 准备最终图表数据
    sorted_years = sorted(annual_data.keys(), reverse=True)
    chart_data = []
    
    for year in sorted_years:
        products = sorted_products_by_year.get(year, [])
        year_bar_data = bar_data[year]
        
        # 确保所有bar数据长度一致
        for item in year_bar_data:
            item["values"] = item["values"][:len(products)] + [0] * (len(products) - len(item["values"]))
        
        # 处理没有产品数据的情况
        if not products and version_list:
            products = ["默认"]
            year_bar_data = [
                {
                    "version": version,
                    "values": [round(yearly_data[year]["values"][version][-1] - yearly_data[year]["values"][version][0])],
                    "products": products
                } 
                for version in version_list if yearly_data[year]["values"][version]
            ]
        
        # 构建图表数据
        chart_data.append({
            "year": year,
            "versions": version_list,
            "products": products,
            "line_data": {
                "dates": yearly_data[year]["dates"],
                "series": [{"name": item["version"], "values": item["values"]} for item in annual_data[year]]
            },
            "bar_data": year_bar_data
        })

    # 处理五年收益差值数据
    five_year_data = [
        {
            "product": product,
            **{version: five_year_product_data[product][version] for version in version_list},
            "difference": five_year_product_data[product].get(version_list[1], 0) - 
                          five_year_product_data[product].get(version_list[0], 0) if len(version_list) > 1 else 0
        } 
        for product in five_year_products
    ]
    
    # 按差值排序
    sort_key = lambda x: x["difference"] if len(version_list) > 1 else x.get(version_list[0], 0)
    five_year_data.sort(key=sort_key, reverse=True)

    return render(request, 'version_compare.html', {
        "versions": versions,
        "version_list": version_list,
        "chart_data": chart_data,
        "years": sorted_years,
        "five_year_data": five_year_data
    })

def version_compare_product(request, versions, product):

    """比较特定商品在多个版本中的收益数据"""
    # 获取版本数据
    all_version_data, all_version_products, version_list = get_version_data(versions)
    if not all_version_data:
        return HttpResponse("未找到指定版本的数据")

    # 获取商品曲线数据
    product_key = product + "L9_15"
    product_curves = {v: all_version_products.get(v, {}).get(product_key, {}) for v in version_list}
    if not any(product_curves.values()):
        return HttpResponse(f"未找到商品 '{product}' 的数据")

    # 按年份分组日期
    year_range = [str(y) for y in range(2020, min(2026, datetime.now().year + 1))]
    all_dates = set.union(*(set(curve.keys()) for curve in product_curves.values() if curve))
    valid_dates_by_year = {
        year: sorted([d for d in all_dates if d.startswith(year)]) 
        for year in year_range if any(d.startswith(year) for d in all_dates)
    }

    # 创建DataFrame
    df_data = [
        (version, date, float(value or 0))
        for version, curve in product_curves.items()
        for date, value in curve.items()
    ]
    df = pd.DataFrame(df_data, columns=['version', 'date', 'value'])
    df['date'] = pd.to_datetime(df['date'])
    df['year'] = df['date'].dt.year.astype(str)

    # 按年处理数据
    yearly_data = {}
    yearly_difference_data = {}
    
    for year in valid_dates_by_year:
        # 筛选并排序年度数据
        year_df = df[df['year'] == year].sort_values('date')
        dates = [d.strftime("%Y-%m-%d") for d in sorted(year_df['date'].unique())]
        
        # 计算每个版本的相对收益曲线
        series = []
        for version in version_list:
            version_df = year_df[year_df['version'] == version]
            if not version_df.empty:
                base_value = version_df['value'].iloc[0]
                data = (version_df['value'] - base_value).tolist()
            else:
                data = [0] * len(dates)
            series.append({"name": version, "data": data})
            
        yearly_data[year] = {"dates": dates, "series": series}
        
        # 计算版本之间的差值
        if len(version_list) >= 2:
            v1_data, v2_data = series[0]["data"], series[1]["data"]
            yearly_difference_data[year] = {
                "name": f"{version_list[1]} - {version_list[0]} 差值",
                "data": [v2 - v1 for v1, v2 in zip(v1_data, v2_data)],
                "dates": dates
            }

    # 处理五年连续数据
    five_year_df = df[df['year'].isin(year_range)].sort_values('date')
    all_continuous_dates = sorted(five_year_df['date'].unique())
    base_values = five_year_df.groupby('version')['value'].first()
    
    # 计算相对收益曲线
    five_year_continuous_data = {
        "dates": [d.strftime("%Y-%m-%d") for d in all_continuous_dates],
        "series": []
    }
    
    for version, group in five_year_df.groupby('version'):
        if group.empty:
            data = [0] * len(all_continuous_dates)
        else:
            data = (group['value'] - base_values.get(version, 0)).tolist()
        five_year_continuous_data["series"].append({"name": version, "data": data})

    # 计算差值曲线
    five_year_continuous_diff_data = None
    if len(version_list) >= 2 and len(five_year_continuous_data["series"]) >= 2:
        v1_data = five_year_continuous_data["series"][0]["data"]
        v2_data = five_year_continuous_data["series"][1]["data"]
        five_year_continuous_diff_data = {
            "name": f"{version_list[1]} - {version_list[0]} 差值",
            "data": [v2 - v1 for v1, v2 in zip(v1_data, v2_data)]
        }

    # 计算五年总收益
    five_year_total = []
    for version in version_list:
        version_df = five_year_df[five_year_df['version'] == version]
        if not version_df.empty:
            five_year_total.append(version_df['value'].iloc[-1] - base_values.get(version, 0))
        else:
            five_year_total.append(0)
            
    five_year_diff = five_year_total[1] - five_year_total[0] if len(five_year_total) >= 2 else None

    # 商品合约大小字典
    product_contracts = {'RM': 50, 'M': 50, 'JD': 25, 'A': 50, 'C': 30, 'CS': 50, 'PK': 16, 'OI': 16,
                         'I': 20, 'JM': 8, 'SF': 35, 'SM': 35, 'MA': 35, 'EB': 16, 'V': 25, 'SA': 25,
                         'FG': 50, 'SP': 16, 'UR': 25, 'CF': 30, 'RU': 11, 'LH': 8, 'AL': 10, 'B': 50,
                         'AP': 16, 'CJ': 16, 'P': 12, 'Y': 12, 'J': 4, 'AU': 4, 'SC': 3, 'TF': 10, 
                         'TS': 10, 'T': 5, 'TL': 3, 'AG': 15, 'AO': 20, 'NI': 8, 'CU': 5, 'SN': 12,
                         'BR': 10, 'LU': 6, 'EC': 6, 'LC': 20, 'SI': 30, 'SH': 7, 'ZN': 15, 'PB': 12,
                         'FU': 10, 'L': 16, 'RB': 25, 'HC': 25, 'BU': 16, 'TA': 36, 'PP': 16, 'EG': 16,
                         'PX': 7, 'PG': 6, 'SR': 16}#Version1、2、3 手数

    return render(request, 'product_curve_compare.html', {
        'version_list': json.dumps(version_list),
        'version_list_raw': version_list,
        'product': product,
        'product_key': product_key,
        'yearly_data': json.dumps(yearly_data),
        'yearly_difference_data': json.dumps(yearly_difference_data),
        'five_year_data': json.dumps({"versions": version_list, "total": five_year_total}),
        'five_year_diff': five_year_diff,
        'five_year_continuous_data': json.dumps(five_year_continuous_data),
        'five_year_continuous_diff_data': json.dumps(five_year_continuous_diff_data),
        'year_range': json.dumps([y for y in year_range if y in yearly_data]),
        'product_contracts': product_contracts
    })

# def annual_rank(request, version):
    """年度收益排行榜视图函数"""
    # 获取系统配置和构建查询条件
    data = System.objects.get(name=version)
    profuctlist = data.tradelist.keys()
    q_objects = []
    weight = {}
    
    # 准备查询条件和权重计算
    for product in profuctlist:
        args = data.tradelist[product]["args"]
        name = product+"L9_15"
        argslist = []
        weight[name] = {}
        wt = data.tradelist[product]["number"]/len(eval(args))
        
        for i in eval(args):
            arg_str = str(i[:-1] if i[-1] <= 0 else i)
            argslist.append(arg_str)
            weight[name][arg_str] = (i[-1] * wt if i[-1] <= 0 else wt)
            
        q_objects.append(Q(name=name, args__in=argslist))
                
    products = Product.objects.filter(reduce(operator.or_, q_objects))
    
    # 存储结果字典
    result = {}  # 所有品种合并结果
    resultper = {}  # 各品种单独结果
    
    # 处理产品数据，收集每个日期的收益
    for product in products:
        name = product.name
        args = product.args
        product_key = name.replace("L9_15", "")
        resultper.setdefault(product_key, {})
        
        for item in product.value:
            dt = item["date"].strftime("%Y-%m-%d")
            value = item["values"] * weight[name][args]
            
            # 存储单品种数据
            resultper[product_key][dt] = resultper[product_key].get(dt, 0) + value
            
            # 合并所有品种数据
            result[dt] = result.get(dt, 0) + value
    
    # 按年份分组处理数据
    yearly_data = {}
    datelist = sorted(result.keys())
    
    # 按年份组织数据
    for dt in datelist:
        year = datetime.strptime(dt, "%Y-%m-%d").year
        
        if year not in yearly_data:
            yearly_data[year] = {"dates": [], "values": {}, "first_date": dt, "last_date": dt}
        
        yearly_data[year]["dates"].append(dt)
        yearly_data[year]["last_date"] = dt
        
        # 收集每个品种在该日期的值
        for product_key in resultper:
            yearly_data[year].setdefault("values", {}).setdefault(product_key, [])
            yearly_data[year]["values"][product_key].append(resultper[product_key].get(dt, 0))
    
    # 计算年度收益并准备图表数据
    annual_data = {}
    for year, data_points in yearly_data.items():
        annual_data[year] = []
        
        for product_key, values in data_points["values"].items():
            if not values:
                continue
                
            first_value = values[0]
            annual_data[year].append({
                "product": product_key,
                "return": values[-1] - first_value,
                "start_date": data_points["first_date"],
                "end_date": data_points["last_date"],
                "values": [v - first_value for v in values]  # 相对收益曲线
            })
    
    # 准备最终图表数据
    sorted_years = sorted(annual_data.keys(), reverse=True)
    chart_data = []
    
    for year in sorted_years:
        # 按收益降序排序
        annual_data[year].sort(key=lambda x: x["return"], reverse=True)
        
        chart_data.append({
            'year': year,
            'products': [item["product"] for item in annual_data[year]],
            'returns': [item["return"] for item in annual_data[year]],
            'line_data': {
                'dates': yearly_data[year]["dates"],
                'series': [{'name': item["product"], 'values': item["values"]} 
                          for item in annual_data[year]]
            }
        })
    
    return render(request, 'annual_rank.html', {
        "version": version,
        "chart_data": chart_data,
        "years": sorted_years
    })
