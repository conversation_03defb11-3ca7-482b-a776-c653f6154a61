{% load custom_filters %}
<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>策略分析</title>
    <script src="/static/echarts.js"></script>
    <style>
      .stats-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
      }
      .stats-table th, .stats-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: right;
      }
      .stats-table th {
        background-color: #f5f5f5;
        position: sticky;
        top: 0;
        z-index: 10;
      }
      /* 固定列宽样式 */
      .strategy-name-col {
        width: 180px;
        min-width: 180px;
        max-width: 180px;
      }
      .total-col {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        background-color: #e0e0e0;
        font-weight: bold;
      }
      .data-col {
        width: 80px;
        min-width: 80px;
        max-width: 80px;
      }
      /* 添加新的样式类，确保所有表格中的列宽一致 */
      .first-col {
        width: 180px;
        min-width: 180px;
        max-width: 180px;
      }
      .last-col {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        background-color: #e0e0e0;
        font-weight: bold;
      }
      .middle-col {
        /* 中间列自适应，平均分配剩余宽度 */
        width: auto;
      }
      .table-container {
        overflow-x: auto;
        margin-top: 20px;
      }
      /* 年份标题样式 */
      .year-header {
        background-color: #4a4a4a;
        color: white;
        font-weight: bold;
        text-align: center;
        padding: 10px;
        margin-top: 20px;
        position: sticky;
        top: 0;
        z-index: 20;
      }
      /* 策略名称列固定样式 */
      .strategy-name {
        font-weight: bold;
        background-color: #f0f0f0;
        position: sticky;
        left: 0;
        z-index: 5;
      }
      /* 懒加载样式 */
      .year-container {
        display: none;
      }
      .year-container.active {
        display: block;
      }
      /* 年份选择器样式 */
      .year-selector {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin: 20px 0;
      }
      .year-btn {
        padding: 8px 15px;
        background-color: #f0f0f0;
        border: 1px solid #ddd;
        cursor: pointer;
        border-radius: 4px;
      }
      .year-btn.active {
        background-color: #4a4a4a;
        color: white;
      }
      /* 加载指示器 */
      .loading {
        text-align: center;
        padding: 20px;
      }
      /* 颜色指示器样式 */
      .color-indicator {
        display: inline-block;
        width: 10px;
        height: 10px;
        margin-right: 5px;
        border-radius: 2px;
        vertical-align: middle;
      }
      /* 回撤表格样式 */
      .drawdown-title {
        margin-top: 30px;
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
      .drawdown-period {
        font-size: 0.9em;
        color: #666;
      }
    </style>
  </head>
  <body>
    <!-- 日期选择器 -->
    <div style="padding: 15px; background-color: #f5f5f5; margin-bottom: 10px;">
      <form id="date-range-form" method="get" action="">
        <div style="display: flex; justify-content: center; gap: 20px; align-items: center;">
          <div>
            <label for="start-date">开始日期:</label>
            <input type="date" id="start-date" name="start_date" value="{{ start_date }}" min="{{ data_start_date }}" max="{{ data_end_date }}"/>
          </div>
          <div>
            <label for="end-date">结束日期:</label>
            <input type="date" id="end-date" name="end_date" value="{{ end_date }}" min="{{ start_date }}" max="{{ data_end_date }}"/>
          </div>
          <button type="submit" style="padding: 5px 15px; background-color: #4a4a4a; color: white; border: none; border-radius: 4px; cursor: pointer;">应用过滤</button>
          <a href="?" style="padding: 5px 15px; background-color: #e0e0e0; color: #333; text-decoration: none; border-radius: 4px;">重置</a>
        </div>
      </form>
    </div>
    
    <!-- 图表部分 -->
    <div id="main" style="width: 100%;height:90vh;"></div>
    
    <!-- 表格部分 -->
    <div class="table-container">
      <h3>月度统计数据</h3>
      
      <!-- 年份选择器 -->
      <div class="year-selector">
        <div class="year-btn active" data-year="all">所有年份</div>
        {% for year in months_by_year.keys %}
          {% if year != 'all' %}
            <div class="year-btn" data-year="{{ year }}">{{ year }}年</div>
          {% endif %}
        {% endfor %}
      </div>
      
      <!-- 所有年份的表格内容 -->
      <div id="year-all" class="year-container active">
        <div class="year-header">{{ start_year }}年至{{ end_year }}年统计</div>
        <table class="stats-table">
          <thead>
            <tr>
              <th class="first-col">策略/年份</th>
              {% for year in months_by_year.keys %}
              {% if year != 'all' %}
              <th class="middle-col">{{ year }}年</th>
              {% endif %}
              {% endfor %}
              <th class="last-col">总收益率</th>
              <th class="last-col">总收益回撤比</th>
            </tr>
          </thead>
          <tbody>
            {% for strategy_name, stats in yearly_stats.items %}
            <!-- 年度收益率 -->
            <tr data-strategy="{{ strategy_name }}">
              <td class="strategy-name first-col">{{ strategy_name|version_to_strategy }}</td>
              {% for year in months_by_year.keys %}
              {% if year != 'all' %}
              <td class="middle-col">
                {% if stats|get_item:year|get_item:'rate' != None and stats|get_item:year|get_item:'rate' != '-' %}
                  {{ stats|get_item:year|get_item:'rate' }}%
                {% else %}
                  -
                {% endif %}
              </td>
              {% endif %}
              {% endfor %}
              <td class="last-col">
                {% if all_time_stats|get_item:strategy_name|get_item:'rate' != None and all_time_stats|get_item:strategy_name|get_item:'rate' != '-' %}
                  {{ all_time_stats|get_item:strategy_name|get_item:'rate' }}%
                {% else %}
                  -
                {% endif %}
              </td>
              <td class="last-col">
                {% if all_time_stats|get_item:strategy_name|get_item:'return_drawdown_ratio' != None and all_time_stats|get_item:strategy_name|get_item:'return_drawdown_ratio' != '-' %}
                  {{ all_time_stats|get_item:strategy_name|get_item:'return_drawdown_ratio' }}
                {% else %}
                  -
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        
        <!-- 所有年份回撤统计表格 -->
        <h4 class="drawdown-title">历史最大回撤统计</h4>
        <table class="stats-table">
          <thead>
            <tr>
              <th>策略名称</th>
              <th>最大回撤(绝对值)</th>
              <th>最大回撤率(%)</th>
              <th>最大回撤区间</th>
              <th>持续天数</th>
              <th>第二大回撤(绝对值)</th>
              <th>第二大回撤率(%)</th>
              <th>第二大回撤区间</th>
              <th>持续天数</th>
              <th>第三大回撤(绝对值)</th>
              <th>第三大回撤率(%)</th>
              <th>第三大回撤区间</th>
              <th>持续天数</th>
            </tr>
          </thead>
          <tbody>
            {% for strategy_name, stats in all_time_drawdown_stats.items %}
            <tr data-strategy="{{ strategy_name }}">
              <td class="strategy-name">{{ strategy_name|version_to_strategy }}</td>
              <td>{{ stats|get_item:'max_drawdown'|get_item:'value'|default:'-' }}</td>
              <td>{{ stats|get_item:'max_drawdown'|get_item:'rate'|default:'-' }}%</td>
              <td class="drawdown-period">{{ stats|get_item:'max_drawdown'|get_item:'period'|default:'-' }}</td>
              <td>{{ stats|get_item:'max_drawdown'|get_item:'duration_days'|default:'-' }}</td>
              <td>{{ stats|get_item:'second_drawdown'|get_item:'value'|default:'-' }}</td>
              <td>{{ stats|get_item:'second_drawdown'|get_item:'rate'|default:'-' }}%</td>
              <td class="drawdown-period">{{ stats|get_item:'second_drawdown'|get_item:'period'|default:'-' }}</td>
              <td>{{ stats|get_item:'second_drawdown'|get_item:'duration_days'|default:'-' }}</td>
              <td>{{ stats|get_item:'third_drawdown'|get_item:'value'|default:'-' }}</td>
              <td>{{ stats|get_item:'third_drawdown'|get_item:'rate'|default:'-' }}%</td>
              <td class="drawdown-period">{{ stats|get_item:'third_drawdown'|get_item:'period'|default:'-' }}</td>
              <td>{{ stats|get_item:'third_drawdown'|get_item:'duration_days'|default:'-' }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
        
        <!-- 添加留白区域，方便用户将回撤表格滚动到屏幕中间 -->
        <div class="table-spacer" style="height: 50vh;"></div>
      </div>
      
      <!-- 各年份的表格内容 -->
      {% for year, months in months_by_year.items %}
        {% if year != 'all' %}
          <div id="year-{{ year }}" class="year-container">
            <div class="year-header">{{ year }}年</div>
            <table class="stats-table">
              <thead>
                <tr>
                  <th class="first-col">策略/月份</th>
                  {% for month in months %}
                  <th class="middle-col">{{ month|slice:"5:" }}</th>
                  {% endfor %}
                  <th class="last-col">年度总计</th>
                  <th class="last-col">年度收益回撤比</th>
                </tr>
              </thead>
              <tbody>
                {% for strategy_name, stats in monthly_stats.items %}
                <!-- 月度收益率 -->
                <tr data-strategy="{{ strategy_name }}">
                 
                  <td class="strategy-name first-col">{{ strategy_name|version_to_strategy }} </td> -->
                  {% for month in months %}
                  <td class="middle-col">
                    {% if stats|get_item:month|get_item:'rate' != None and stats|get_item:month|get_item:'rate' != '-' %}
                      {{ stats|get_item:month|get_item:'rate' }}%
                    {% else %}
                      -
                    {% endif %}
                  </td>
                  {% endfor %}
                  <td class="last-col">
                    {% if yearly_stats|get_item:strategy_name|get_item:year|get_item:'rate' != None and yearly_stats|get_item:strategy_name|get_item:year|get_item:'rate' != '-' %}
                      {{ yearly_stats|get_item:strategy_name|get_item:year|get_item:'rate' }}%
                    {% else %}
                      -
                    {% endif %}
                  </td>
                  <td class="last-col">
                    {% if yearly_stats|get_item:strategy_name|get_item:year|get_item:'return_drawdown_ratio' != None and yearly_stats|get_item:strategy_name|get_item:year|get_item:'return_drawdown_ratio' != '-' %}
                      {{ yearly_stats|get_item:strategy_name|get_item:year|get_item:'return_drawdown_ratio' }}
                    {% else %}
                      -
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
            
            <!-- 年度回撤统计表格 -->
            <h4 class="drawdown-title">{{ year }}年回撤统计</h4>
            <table class="stats-table">
              <thead>
                <tr>
                  <th>策略名称</th>
                  <th>最大回撤(绝对值)</th>
                  <th>最大回撤率(%)</th>
                  <th>最大回撤区间</th>
                  <th>持续天数</th>
                  <th>第二大回撤(绝对值)</th>
                  <th>第二大回撤率(%)</th>
                  <th>第二大回撤区间</th>
                  <th>持续天数</th>
                  <th>第三大回撤(绝对值)</th>
                  <th>第三大回撤率(%)</th>
                  <th>第三大回撤区间</th>
                  <th>持续天数</th>
                </tr>
              </thead>
              <tbody>
                {% for strategy_name, stats in yearly_drawdown_stats.items %}
                  {% if stats|get_item:year %}
                  <tr data-strategy="{{ strategy_name }}">
                    <td class="strategy-name">{{ strategy_name|version_to_strategy }}</td>
                    <td>{{ stats|get_item:year|get_item:'max_drawdown'|get_item:'value'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'max_drawdown'|get_item:'rate'|default:'-' }}%</td>
                    <td class="drawdown-period">{{ stats|get_item:year|get_item:'max_drawdown'|get_item:'period'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'max_drawdown'|get_item:'duration_days'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'second_drawdown'|get_item:'value'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'second_drawdown'|get_item:'rate'|default:'-' }}%</td>
                    <td class="drawdown-period">{{ stats|get_item:year|get_item:'second_drawdown'|get_item:'period'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'second_drawdown'|get_item:'duration_days'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'third_drawdown'|get_item:'value'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'third_drawdown'|get_item:'rate'|default:'-' }}%</td>
                    <td class="drawdown-period">{{ stats|get_item:year|get_item:'third_drawdown'|get_item:'period'|default:'-' }}</td>
                    <td>{{ stats|get_item:year|get_item:'third_drawdown'|get_item:'duration_days'|default:'-' }}</td>
                  </tr>
                  {% endif %}
                {% endfor %}
              </tbody>
            </table>
            
            <!-- 添加留白区域，方便用户将回撤表格滚动到屏幕中间 -->
            <div class="table-spacer" style="height: 50vh;"></div>
          </div>
        {% endif %}
      {% endfor %}
    </div>

    <!-- 修改图表标题 -->
    <script type="text/javascript">
      // 图表初始化
      var chartDom = document.getElementById('main');
      var myChart = echarts.init(chartDom);
      
      // 预先定义颜色数组，避免重复获取
      var defaultColors = [
        '#1a56db', // 深蓝色
        '#046c4e', // 深绿色
        '#c81e1e', // 深红色
        '#ee6666', '#73c0de',
        '#3ba272', '#fc8452', '#9a60b4'
      ];
      
      // 存储原始数据，用于年份筛选
      var originalData = {
        xAxis: [{% for i in x%} '{{i}}',{%endfor%}],
        series: [
          {% for i in dataset%}
          {
            name: '{{i.name|version_to_strategy}}',
            data: [
              {% for k,v in i.value.items %}
              {
                date: '{{k}}',
                value: {{v}}
              },
              {% endfor %}
            ]
          },
          {%endfor%}
        ]
      };
      
      // 扩展日期范围到指定年底，保持均匀的工作日分布
      function extendDateRange(dates, endYear) {
        if (!dates || dates.length === 0) return [];
        
        // 获取最后一个日期
        var lastDate = dates[dates.length - 1];
        var lastDateObj = new Date(lastDate);
        
        // 创建目标结束日期
        var targetEndDate = new Date(endYear + '-12-31');
        
        // 如果最后一个日期已经超过了目标日期，则不需要扩展
        if (lastDateObj >= targetEndDate) return dates;
        
        // 创建扩展后的日期数组
        var extendedDates = dates.slice();
        
        // 计算日期间隔 - 假设原始数据是工作日数据
        // 找到最后几个日期点的平均间隔
        var sampleSize = Math.min(20, dates.length - 1); // 使用最后20个点或更少
        var totalDays = 0;
        
        for (var i = dates.length - sampleSize; i < dates.length - 1; i++) {
          var currentDate = new Date(dates[i]);
          var nextDate = new Date(dates[i + 1]);
          var diffDays = Math.round((nextDate - currentDate) / (1000 * 60 * 60 * 24));
          totalDays += diffDays;
        }
        
        // 计算平均间隔天数，默认为1个工作日
        var avgDayInterval = Math.max(1, Math.round(totalDays / sampleSize));
        
        // 从最后一个日期开始，按照平均间隔添加日期点，直到目标结束日期
        var currentDate = new Date(lastDate);
        
        while (true) {
          // 添加平均间隔天数
          currentDate = new Date(currentDate.getTime() + avgDayInterval * 24 * 60 * 60 * 1000);
          
          // 如果超过目标日期，则停止
          if (currentDate > targetEndDate) {
            break;
          }
          
          // 格式化日期为YYYY-MM-DD
          var year = currentDate.getFullYear();
          var month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
          var day = currentDate.getDate().toString().padStart(2, '0');
          var dateStr = year + '-' + month + '-' + day;
          
          // 添加到扩展日期数组
          extendedDates.push(dateStr);
        }
        
        // 确保最后一个日期是目标年的12月31日
        extendedDates.push(endYear + '-12-31');
        
        return extendedDates;
      }
      
      // 根据结束日期动态确定显示范围
      var endDateString = '{{ end_date }}';
      var endDateObj = new Date(endDateString);
      var endYear = endDateObj.getFullYear();
      
      // X轴数据不再扩展到固定年份，而是直接使用数据范围
      var extendedXAxis = originalData.xAxis;
      
      // 初始化图表选项
      var option = {
        color: defaultColors, // 明确指定颜色数组
        title: {
          text: '{{ start_year }}年至{{ end_year }}年收益曲线',  // 使用后端传递的开始年份
          left: 'center',  // 标题居中
          top: 10,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          textStyle: {
            fontSize: '12' 
          },
          formatter: function(params) {
            var result = params[0].axisValue + '<br/>';
            params.forEach(function(item) {
              // 将数值格式化为整数
              if (item.value !== undefined && item.value !== null) {
                var value = Math.round(item.value);
                result += item.marker + ' ' + item.seriesName + ': ' + value.toLocaleString() + '<br/>';
              }
            });
            return result;
          }
        },
        legend: {
          data: [{% for i in dataset%} '{{i.name|version_to_strategy}}',{%endfor%}],
          selectedMode: 'multiple',
          itemWidth: 10,
          itemHeight: 10,
          orient: 'horizontal',
          left: 'center',  // 图例居中
          top: 40,  // 调整图例位置，放在标题下方
          textStyle: {
            fontSize: '12px'
          }
        },
        grid: {
          left: '3%',
          right: '10%',
          bottom: '12%',
          top: '90px',  // 增加顶部空间，为居中的图例和标题留出位置
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {},
            dataZoom: {},
            restore: {}
          },
          right: '20px',  // 工具箱靠右放置
          top: '20px'
        },
        xAxis: {
          type: 'category',  // 保持使用category类型
          axisLine: {
            lineStyle: {width: 1}
          },
          boundaryGap: false,
          data: extendedXAxis,  // 使用扩展后的X轴数据
          axisLabel: {
            formatter: function(value) {
              // 解析日期
              var date = new Date(value);
              var year = date.getFullYear();
              var month = date.getMonth() + 1;
              var day = date.getDate();
              
              // 获取当前选择的年份按钮
              var activeYearBtn = document.querySelector('.year-btn.active');
              var selectedYear = activeYearBtn ? activeYearBtn.getAttribute('data-year') : 'all';
              
              // 单独年份显示时，格式化为季度末日期
              if (selectedYear !== 'all') {
                // 判断是否是季度末
                var isQuarterEnd = (month === 3 && day === 31) || 
                                   (month === 6 && day === 30) || 
                                   (month === 9 && day === 30) || 
                                   (month === 12 && day === 31);
                
                // 季度末或其他需要显示的日期都使用年份-月份格式
                return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day);
              } else {
                // 所有年份视图，显示年-月格式
                return year + '-' + (month < 10 ? '0' + month : month);
              }
            },
            interval: function(index, value) {
              // 获取当前选择的年份按钮
              var activeYearBtn = document.querySelector('.year-btn.active');
              var selectedYear = activeYearBtn ? activeYearBtn.getAttribute('data-year') : 'all';
              
              // 始终显示第一个和最后一个标签
              if (index === 0 || index === extendedXAxis.length - 1) {
                return true;
              }
              
              // 单独年份视图 - 按季度显示
              if (selectedYear !== 'all') {
                var date = new Date(value);
                var year = date.getFullYear();
                var month = date.getMonth() + 1;
                var day = date.getDate();
                
                // 只有当前年份的数据
                if (year.toString() !== selectedYear) {
                  return false;
                }
                
                // 季度末日期 (3/31, 6/30, 9/30, 12/31)
                var isQuarterEnd = (month === 3 && day === 31) || 
                                   (month === 6 && day === 30) || 
                                   (month === 9 && day === 30) || 
                                   (month === 12 && day === 31);
                
                // 特殊处理：检查是否是当前季度的最后一个有数据的日期
                if (!isQuarterEnd) {
                  // 获取当前日期所在季度
                  var currentQuarter = Math.ceil(month / 3);
                  
                  // 查找当前索引之后的数据点
                  var nextQuarterFound = false;
                  for (var i = index + 1; i < extendedXAxis.length; i++) {
                    var nextDate = new Date(extendedXAxis[i]);
                    var nextMonth = nextDate.getMonth() + 1;
                    var nextQuarter = Math.ceil(nextMonth / 3);
                    
                    // 如果找到了下一个季度的数据点
                    if (nextQuarter > currentQuarter) {
                      nextQuarterFound = true;
                      break;
                    }
                  }
                  
                  // 如果已经找到下一个季度的数据点，且当前是本季度最后一个点
                  if (nextQuarterFound) {
                    // 检查是否是本季度的最后一个数据点
                    var isLastPointInQuarter = true;
                    for (var i = index + 1; i < extendedXAxis.length; i++) {
                      var checkDate = new Date(extendedXAxis[i]);
                      var checkMonth = checkDate.getMonth() + 1;
                      var checkQuarter = Math.ceil(checkMonth / 3);
                      
                      if (checkQuarter === currentQuarter) {
                        isLastPointInQuarter = false;
                        break;
                      }
                    }
                    
                    return isLastPointInQuarter;
                  }
                }
                
                return isQuarterEnd;
              } else {
                // 所有年份视图 - 最多显示10个点
                // 如果数据很少，则全部显示
                if (extendedXAxis.length <= 10) {
                  return true;
                }
                
                // 超过10个数据点时，固定显示10个标签（包含首尾两个点）
                const maxLabels = 10;
                
                // 计算显示间隔 - 确保平均分布
                const interval = Math.floor(extendedXAxis.length / (maxLabels - 1));
                
                // 只显示固定间隔的标签
                return index % interval === 0;
              }
            }
          }
        },
        dataZoom: [
          {
            type: 'slider',
            xAxisIndex: 0,
            filterMode: 'none',
            start: 0,
            end: 100
          },
          {
            type: 'inside',
            xAxisIndex: 0,
            filterMode: 'none'
          }
        ],
        yAxis: {
          min: 'dataMin',
          max: 'dataMax',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            fontSize: 14,
            padding: [0, 0, 10, 0]
          }
        },
        series: originalData.series.map(function(item) {
          // 为每个系列创建与扩展X轴对应的数据数组
          var seriesData = new Array(extendedXAxis.length).fill(null);
          
          // 填充实际数据点
          item.data.forEach(function(d) {
            var index = extendedXAxis.indexOf(d.date);
            if (index !== -1) {
              seriesData[index] = d.value;
            }
          });
          
          return {
            name: item.name,
            type: 'line',
            sampling: 'lttb',
            data: seriesData,
            lineStyle: {width: 1.5},
            symbol: 'none',
            connectNulls: false, // 不连接空值点，这样曲线就会在最后一个有效数据点处停止
            emphasis: {
              focus: 'series',
              lineStyle: {width: 2.5}
            }
          };
        })
      };

      // 设置图表选项
      myChart.setOption(option);
      
      // 获取图表颜色并应用到表格 - 优化版本
      function applyChartColorsToTable() {
        // 缓存DOM查询结果
        var strategyRows = document.querySelectorAll('tr[data-strategy]');
        var strategyNames = {};
        
        // 收集所有策略名称并去重
        strategyRows.forEach(function(row) {
          strategyNames[row.getAttribute('data-strategy')] = true;
        });
        
        // 获取ECharts的颜色数组
        var colors = myChart._model && myChart._model.option.color || defaultColors;
        
        // 为每个策略行应用颜色
        Object.keys(strategyNames).forEach(function(name, index) {
          var color = colors[index % colors.length];
          var lightColor = 'rgba(' + hexToRgb(color) + ',0.15)';
          
          // 查找该策略的所有行
          var rows = document.querySelectorAll('tr[data-strategy="' + name + '"]');
          rows.forEach(function(row) {
            row.style.backgroundColor = lightColor;
            
            // 添加颜色指示器到第一个单元格
            var firstCell = row.querySelector('td.strategy-name');
            if (firstCell && !firstCell.querySelector('.color-indicator')) {
              var indicator = document.createElement('span');
              indicator.className = 'color-indicator';
              indicator.style.backgroundColor = color;
              firstCell.insertBefore(indicator, firstCell.firstChild);
            }
          });
        });
      }
      
      // 将十六进制颜色转换为RGB
      function hexToRgb(hex) {
        // 移除#号
        hex = hex.replace(/^#/, '');
        
        // 解析RGB值
        var bigint = parseInt(hex, 16);
        var r = (bigint >> 16) & 255;
        var g = (bigint >> 8) & 255;
        var b = bigint & 255; 
        
        return r + ',' + g + ',' + b;
      }
      
      // 根据年份筛选数据并更新图表
      function updateChartByYear(year) {
        var title = year === 'all' ? '{{ start_year }}年至{{ end_year }}年收益曲线' : year + '年收益曲线';

        if (year === 'all') {
          // 显示所有数据
          myChart.setOption({
            color: defaultColors, // 确保使用相同的颜色数组
            title: {
              text: title,
              left: 'center'  // 确保标题居中
            },
            xAxis: {
              type: 'category',  // 保持使用category类型
              data: originalData.xAxis  // 使用原始X轴数据，不扩展
            },
            yAxis: {
              min: 'dataMin',
              max: 'dataMax',
              nameLocation: 'middle',
              nameGap: 50
            },
            series: originalData.series.map(function(item) {
              return {
                name: item.name,
                type: 'line',
                sampling: 'lttb',
                data: item.data.map(function(d) { return Math.round(d.value); }),
                lineStyle: {width: 1.5},
                symbol: 'none',
                connectNulls: false,
                emphasis: {
                  focus: 'series',
                  lineStyle: {width: 2.5}
                }
              };
            })
          });
        } else {
          // 筛选特定年份的数据
          var yearStart = year + '-01-01';
          var yearEnd = year + '-12-31';
          var filteredXAxis = [];
          var filteredSeries = [];

          // 设置年份的开始和结束日期
          var startDate = new Date(yearStart);
          var endDate = new Date(yearEnd);

          // 判断是否使用扩展的X轴数据
          var currentYear = new Date().getFullYear();
          var isExtendedYear = parseInt(year) >= currentYear;
          
          if (isExtendedYear) {
            // 对于当前年份及以后的年份，使用扩展的X轴数据
            extendedXAxis.forEach(function(date) {
              var dateObj = new Date(date);
              if (dateObj >= startDate && dateObj <= endDate) {
                filteredXAxis.push(date);
              }
            });
          } else {
            // 对于过去年份，使用原始数据
            originalData.xAxis.forEach(function(date) {
              var dateObj = new Date(date);
              if (dateObj >= startDate && dateObj <= endDate) {
                filteredXAxis.push(date);
              }
            });
          }

          // 如果没有该年的数据，创建一个包含全年的X轴
          if (filteredXAxis.length === 0) {
            // 创建该年每月的第一天作为X轴点
            for (var month = 1; month <= 12; month++) {
              var monthStr = month < 10 ? '0' + month : '' + month;
              filteredXAxis.push(year + '-' + monthStr + '-01');
            }
          }

          originalData.series.forEach(function(series) {
            var yearData = [];
            var baseValue = null;
            var yearDataPoints = [];

            // 先收集该年的所有数据点
            series.data.forEach(function(item) {
              var date = new Date(item.date);

              // 检查日期是否在指定年份范围内
              if (date >= startDate && date <= endDate) {
                yearDataPoints.push(item);
              }
            });

            // 按日期排序确保顺序正确
            yearDataPoints.sort(function(a, b) {
              return new Date(a.date) - new Date(b.date);
            });

            // 确保有数据点
            if (yearDataPoints.length > 0) {
              // 找到去年年末的累积收益作为基准
              var prevYearEnd = (parseInt(year) - 1) + '-12-31';
              var foundBaseValue = false;
              
              // 先尝试找去年年末的精确日期
              for (var i = 0; i < series.data.length; i++) {
                if (series.data[i].date === prevYearEnd) {
                  baseValue = series.data[i].value;
                  foundBaseValue = true;
                  break;
                }
              }
              
              // 如果没找到去年年末的精确日期，找去年最后一个有数据的日期
              if (!foundBaseValue) {
                var prevYearStart = (parseInt(year) - 1) + '-01-01';
                var prevYearData = [];
                
                for (var i = 0; i < series.data.length; i++) {
                  var itemDate = new Date(series.data[i].date);
                  var prevStart = new Date(prevYearStart);
                  var yearStart = new Date(year + '-01-01');
                  
                  if (itemDate >= prevStart && itemDate < yearStart) {
                    prevYearData.push(series.data[i]);
                  }
                }
                
                if (prevYearData.length > 0) {
                  // 按日期排序，取最后一个
                  prevYearData.sort(function(a, b) {
                    return new Date(a.date) - new Date(b.date);
                  });
                  baseValue = prevYearData[prevYearData.length - 1].value;
                } else {
                  // 如果没有去年的数据，使用0作为基准
                  baseValue = 0;
                }
              }

              // 创建与筛选后X轴对应的数据数组
              var seriesData = new Array(filteredXAxis.length).fill(null);

              // 填充实际数据点
              yearDataPoints.forEach(function(item) {
                var index = filteredXAxis.indexOf(item.date);
                if (index !== -1) {
                  // 计算年度累积收益：当前累积收益 - 去年年末累积收益
                  seriesData[index] = Math.round(item.value - baseValue);
                }
              });

              filteredSeries.push({
                name: series.name,
                type: 'line',
                sampling: 'lttb',
                data: seriesData,
                lineStyle: {width: 1.5},
                symbol: 'none',
                connectNulls: false, // 不连接空值点
                emphasis: {
                  focus: 'series',
                  lineStyle: {width: 2.5}
                }
              });
            }
          });

          // 确保有数据可显示
          myChart.setOption({
            title: {
              text: title,
              left: 'center'  // 确保标题居中
            },
            xAxis: {
              type: 'category',  // 保持使用category类型
              data: filteredXAxis
            },
            yAxis: {
              nameLocation: 'middle',
              nameGap: 50,
              axisLabel: {
                formatter: '{value}'
              }
            },
            series: filteredSeries.length > 0 ? filteredSeries : []
          });
        }
      }
      // 图表渲染完成后应用颜色
      myChart.on('finished', function() {
        console.log('图表渲染完成');
        applyChartColorsToTable();
      });
      
      // 确保表格在图表渲染前也能显示
      setTimeout(applyChartColorsToTable, 100);
      
      // 窗口大小变化时自动调整图表大小
      window.addEventListener('resize', function() {
        myChart.resize();
      });
      
      // 年份选择器功能
      document.querySelectorAll('.year-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
          // 更新按钮状态
          document.querySelectorAll('.year-btn').forEach(function(b) {
            b.classList.remove('active');
          });
          this.classList.add('active');
          
          // 显示对应年份的表格
          var year = this.getAttribute('data-year');
          document.querySelectorAll('.year-container').forEach(function(container) {
            container.classList.remove('active');
          });
          document.getElementById('year-' + year).classList.add('active');
          
          // 更新图表数据
          updateChartByYear(year);
        });
      });
      
      // 初始化时显示所有数据
      updateChartByYear('all');
    </script>
  </body>
</html>