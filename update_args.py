#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新配置文件脚本
将canshu.json中的商品args参数更新到output.json中，但保持output.json中的number值不变
"""

import json
import re
from collections import OrderedDict

def clean_json_content(content):
    """清理JSON内容，移除MongoDB特有的语法"""
    # 移除ObjectId()包装
    content = re.sub(r'ObjectId\("([^"]+)"\)', r'"\1"', content)
    # 移除NumberInt()包装
    content = re.sub(r'NumberInt\((\d+)\)', r'\1', content)
    return content

def restore_mongodb_format(content):
    """恢复MongoDB格式"""
    # 恢复ObjectId格式
    content = re.sub(r'"([0-9a-f]{24})"', r'ObjectId("\1")', content)
    # 恢复NumberInt格式（对number字段）
    content = re.sub(r'"number"\s*:\s*(\d+)', r'"number" : NumberInt(\1)', content)
    return content

def load_json_file(filepath):
    """加载JSON文件"""
    print(f"正在加载文件: {filepath}")
    try:
        # 尝试不同的编码方式
        encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312']
        content = None
        
        for encoding in encodings:
            try:
                with open(filepath, 'r', encoding=encoding) as f:
                    content = f.read()
                print(f"成功使用 {encoding} 编码读取文件")
                break
            except UnicodeDecodeError:
                continue
        
        if content is None:
            print(f"无法用任何编码读取文件: {filepath}")
            return None
        
        # 检查文件是否为空
        if not content.strip():
            print(f"文件为空: {filepath}")
            return None
        
        print(f"文件内容前100个字符: {content[:100]}")
        
        # 清理MongoDB特有的语法
        clean_content = clean_json_content(content)
        print(f"清理后内容前100个字符: {clean_content[:100]}")
        
        # 解析JSON
        data = json.loads(clean_content, object_pairs_hook=OrderedDict)
        print(f"成功解析JSON，包含 {len(data)} 个顶级键")
        return data
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误 {filepath}: {e}")
        print(f"错误位置附近的内容: {clean_content[max(0, e.pos-50):e.pos+50]}")
        return None
    except Exception as e:
        print(f"加载文件时发生错误 {filepath}: {e}")
        return None

def save_json_file(data, filepath):
    """保存JSON文件"""
    try:
        # 转换为JSON字符串
        json_str = json.dumps(data, ensure_ascii=False, indent=4)
        
        # 恢复MongoDB格式
        json_str = restore_mongodb_format(json_str)
        
        # 保存文件
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(json_str)
        
        print(f"成功保存文件: {filepath}")
        return True
        
    except Exception as e:
        print(f"保存文件时发生错误 {filepath}: {e}")
        return False

def update_args():
    """更新args参数"""
    print("开始更新args参数...")
    
    # 加载canshu.json（args来源）
    canshu_data = load_json_file('canshu.json')
    if canshu_data is None:
        print("无法加载canshu.json，退出")
        return False
    
    # 加载output.json（number来源，需要更新args）
    output_data = load_json_file('output.json')
    if output_data is None:
        print("无法加载output.json，退出")
        return False
    
    # 获取商品配置
    canshu_products = canshu_data.get('tradelist', {})
    output_products = output_data.get('tradelist', {})
    
    print(f"canshu.json中有 {len(canshu_products)} 个商品")
    print(f"output.json中有 {len(output_products)} 个商品")
    
    # 更新计数器
    updated_count = 0
    not_found_count = 0
    
    # 遍历output.json中的每个商品
    for product_name, output_config in output_products.items():
        if product_name in canshu_products:
            # 获取canshu.json中的args
            canshu_args = canshu_products[product_name].get('args', '')
            # 保持output.json中的number不变
            original_number = output_config.get('number', 0)
            
            # 更新output.json中的args，保持number不变
            output_config['args'] = canshu_args
            output_config['number'] = original_number
            
            updated_count += 1
            print(f"已更新 {product_name}: args来自canshu.json, number保持为{original_number}")
        else:
            not_found_count += 1
            print(f"警告: {product_name} 在canshu.json中未找到")
    
    # 保存更新后的output.json
    if save_json_file(output_data, 'output_updated.json'):
        print(f"\n更新完成！")
        print(f"- 成功更新: {updated_count} 个商品")
        print(f"- 未找到: {not_found_count} 个商品")
        print(f"- 结果已保存到: output_updated.json")
        return True
    else:
        print("保存文件失败")
        return False

if __name__ == "__main__":
    success = update_args()
    if success:
        print("脚本执行成功！")
    else:
        print("脚本执行失败！") 