#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据复制脚本
将远程MongoDB服务器的Product数据复制到本地MongoDB服务器
"""

from pymongo import MongoClient
from typing import Dict, Any, Optional
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('product_data_update.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MongoDataMigrator:
    """MongoDB数据迁移类"""

    def __init__(self, source_config: Dict[str, Any], target_config: Dict[str, Any]):
        """
        初始化MongoDB连接配置

        Args:
            source_config: 源数据库配置
            target_config: 目标数据库配置
        """
        self.source_config = source_config
        self.target_config = target_config
        self.source_client = None
        self.target_client = None

    def connect(self) -> bool:
        """
        连接到源和目标MongoDB数据库

        Returns:
            bool: 连接成功返回True，否则返回False
        """
        try:
            # 连接源数据库
            source_uri = f"mongodb://{self.source_config['host']}:{self.source_config['port']}"
            self.source_client = MongoClient(source_uri, serverSelectionTimeoutMS=5000)
            self.source_client.server_info()  # 测试连接
            logger.info(f"成功连接到源数据库: {source_uri}")

            # 连接目标数据库
            target_uri = f"mongodb://{self.target_config['host']}:{self.target_config['port']}"
            self.target_client = MongoClient(target_uri, serverSelectionTimeoutMS=5000)
            self.target_client.server_info()  # 测试连接
            logger.info(f"成功连接到目标数据库: {target_uri}")

            return True

        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False

    def get_collection_stats(self, client: MongoClient, db_name: str, collection_name: str) -> Optional[Dict]:
        """
        获取集合统计信息

        Args:
            client: MongoDB客户端
            db_name: 数据库名称
            collection_name: 集合名称

        Returns:
            Dict: 集合统计信息
        """
        try:
            db = client[db_name]
            collection = db[collection_name]
            stats = {
                'count': collection.count_documents({}),
                'exists': collection_name in db.list_collection_names()
            }
            return stats
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {str(e)}")
            return None

    def migrate_collection(self, source_db: str, source_collection: str,
                           target_db: str, target_collection: str,
                           batch_size: int = 1000, clear_target: bool = False) -> bool:
        """
        迁移集合数据

        Args:
            source_db: 源数据库名称
            source_collection: 源集合名称
            target_db: 目标数据库名称
            target_collection: 目标集合名称
            batch_size: 批处理大小
            clear_target: 是否清空目标集合

        Returns:
            bool: 迁移成功返回True，否则返回False
        """
        try:
            # 获取源和目标集合
            source_coll = self.source_client[source_db][source_collection]
            target_coll = self.target_client[target_db][target_collection]

            # 获取源集合统计信息
            source_stats = self.get_collection_stats(self.source_client, source_db, source_collection)
            if not source_stats or not source_stats['exists']:
                logger.error(f"源集合 {source_db}.{source_collection} 不存在")
                return False

            total_docs = source_stats['count']
            logger.info(f"源集合总文档数: {total_docs}")

            # 如果需要清空目标集合
            if clear_target:
                target_coll.delete_many({})
                logger.info("已清空目标集合")

            # 批量复制数据
            processed_count = 0
            batch_data = []

            logger.info("开始数据迁移...")
            start_time = datetime.now()

            for document in source_coll.find():
                batch_data.append(document)

                # 当批次数据达到指定大小时执行插入
                if len(batch_data) >= batch_size:
                    self._insert_batch(target_coll, batch_data)
                    processed_count += len(batch_data)
                    batch_data = []

                    # 显示进度
                    progress = (processed_count / total_docs) * 100
                    logger.info(f"已处理: {processed_count}/{total_docs} ({progress:.1f}%)")

            # 插入剩余数据
            if batch_data:
                self._insert_batch(target_coll, batch_data)
                processed_count += len(batch_data)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"数据迁移完成!")
            logger.info(f"总处理文档数: {processed_count}")
            logger.info(f"耗时: {duration:.2f}秒")

            # 验证迁移结果
            target_stats = self.get_collection_stats(self.target_client, target_db, target_collection)
            if target_stats:
                logger.info(f"目标集合文档数: {target_stats['count']}")
                if target_stats['count'] == total_docs:
                    logger.info("✅ 数据迁移验证成功")
                    return True
                else:
                    logger.warning(f"⚠️ 数据数量不匹配，源: {total_docs}, 目标: {target_stats['count']}")

            return True

        except Exception as e:
            logger.error(f"数据迁移失败: {str(e)}")
            return False

    def _insert_batch(self, collection, batch_data):
        """
        批量插入数据

        Args:
            collection: 目标集合
            batch_data: 批次数据
        """
        try:
            if batch_data:
                collection.insert_many(batch_data, ordered=False)
        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            # 尝试逐个插入
            for doc in batch_data:
                try:
                    collection.insert_one(doc)
                except Exception as doc_error:
                    logger.error(f"单个文档插入失败: {str(doc_error)}")

    def close_connections(self):
        """关闭数据库连接"""
        if self.source_client:
            self.source_client.close()
            logger.info("已关闭源数据库连接")
        if self.target_client:
            self.target_client.close()
            logger.info("已关闭目标数据库连接")


def main():
    """主函数"""
    # 配置源和目标数据库
    source_config = {
        'host': '************',
        'port': 27017
    }

    target_config = {
        'host': 'localhost',
        'port': 27017
    }

    # 创建迁移器实例
    migrator = MongoDataMigrator(source_config, target_config)

    try:
        # 连接数据库
        if not migrator.connect():
            logger.error("无法连接到数据库，程序退出")
            return

        # 执行数据迁移
        success = migrator.migrate_collection(
            source_db='Dashboard',
            source_collection='Product',
            target_db='Dashboard',
            target_collection='Product',
            batch_size=1000,
            clear_target=True  # 设置为True会清空目标集合后再导入
        )

        if success:
            logger.info("🎉 Product数据更新完成!")
        else:
            logger.error("❌ Product数据更新失败!")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
    finally:
        # 关闭连接
        migrator.close_connections()


if __name__ == "__main__":
    main()