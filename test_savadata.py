#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证增量同步功能
"""

from datetime import datetime
from bson import ObjectId
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from savadata import MongoDataMigrator

def test_date_comparison():
    """测试日期比较功能"""
    print("测试日期比较功能...")
    
    # 创建测试实例
    migrator = MongoDataMigrator({}, {})
    
    # 测试文档1 - 有日期数据
    doc_with_date = {
        "_id": ObjectId("67c7abc30ff4c1f1806cd80d"),
        "args": "[100, 0, 0, 100]",
        "value": [
            {
                "date": datetime(2014, 12, 23, 15, 0, 0),
                "values": 0.0
            },
            {
                "date": datetime(2014, 12, 24, 15, 0, 0),
                "values": 1.0
            }
        ]
    }
    
    # 测试文档2 - 没有日期数据
    doc_without_date = {
        "_id": ObjectId("67c7abc30ff4c1f1806cd80e"),
        "args": "[100, 0, 0, 100]",
        "value": []
    }
    
    # 测试文档3 - 有更新的日期数据
    doc_with_newer_date = {
        "_id": ObjectId("67c7abc30ff4c1f1806cd80d"),
        "args": "[100, 0, 0, 100]",
        "value": [
            {
                "date": datetime(2014, 12, 23, 15, 0, 0),
                "values": 0.0
            },
            {
                "date": datetime(2014, 12, 24, 15, 0, 0),
                "values": 1.0
            },
            {
                "date": datetime(2014, 12, 25, 15, 0, 0),
                "values": 2.0
            }
        ]
    }
    
    # 测试获取最后日期
    last_date1 = migrator.get_document_last_date(doc_with_date)
    last_date2 = migrator.get_document_last_date(doc_without_date)
    last_date3 = migrator.get_document_last_date(doc_with_newer_date)
    
    print(f"文档1最后日期: {last_date1}")
    print(f"文档2最后日期: {last_date2}")
    print(f"文档3最后日期: {last_date3}")
    
    # 测试日期比较
    needs_update1 = migrator.compare_document_dates(doc_with_newer_date, doc_with_date)
    needs_update2 = migrator.compare_document_dates(doc_with_date, doc_without_date)
    needs_update3 = migrator.compare_document_dates(doc_with_date, doc_with_newer_date)
    
    print(f"新文档 vs 旧文档需要更新: {needs_update1}")
    print(f"有日期文档 vs 无日期文档需要更新: {needs_update2}")
    print(f"旧文档 vs 新文档需要更新: {needs_update3}")
    
    # 测试获取更新数据
    newer_values = migrator.get_newer_values(doc_with_newer_date, doc_with_date)
    print(f"需要更新的数据条数: {len(newer_values)}")
    if newer_values:
        print(f"最新数据日期: {newer_values[0]['date']}")
    
    print("日期比较功能测试完成!\n")

def test_connection():
    """测试数据库连接"""
    print("测试数据库连接...")
    
    source_config = {
        'host': '************',
        'port': 27017
    }

    target_config = {
        'host': 'localhost',
        'port': 27017
    }
    
    migrator = MongoDataMigrator(source_config, target_config)
    
    if migrator.connect():
        print("✅ 数据库连接成功!")
        
        # 测试获取集合统计信息
        source_stats = migrator.get_collection_stats(
            migrator.source_client, 'Dashboard', 'Product'
        )
        target_stats = migrator.get_collection_stats(
            migrator.target_client, 'Dashboard', 'Product'
        )
        
        if source_stats:
            print(f"源数据库文档数: {source_stats['count']}")
        if target_stats:
            print(f"目标数据库文档数: {target_stats['count']}")
            
        migrator.close_connections()
    else:
        print("❌ 数据库连接失败!")
    
    print("数据库连接测试完成!\n")

if __name__ == "__main__":
    print("开始测试增量同步功能...\n")
    
    # 测试日期比较功能
    test_date_comparison()
    
    # 测试数据库连接（可选，需要实际的数据库）
    try:
        test_connection()
    except Exception as e:
        print(f"数据库连接测试跳过: {str(e)}\n")
    
    print("所有测试完成!")
