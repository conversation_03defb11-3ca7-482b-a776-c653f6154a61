{
    "_id" : ObjectId("6880d33ea63434333f79e627"),
    "name" : "均衡1.1",
    "tradelist" : {
        "A" : {
            "args" : "[[10,0,0,1000],[5,0,0,1000],[5,0,0,2000],[5,0,0,1500],[20,0,0,400,-1],[10,0,0,700,-1],[100,70,0,400],[100,80,0,400],[100,70,0,300],[100,80,0,300],[100,70,0,200],[100,70,0,900,-1],[100,70,0,800,-1],[95,5,0,100],[100,0,0,100],[100,0,0,700,-1],[100,0,0,600,-1],[90,10,0,1500,-1],[60,40,0,200],[70,30,0,100],[60,40,0,400],[60,40,0,800],[70,30,0,200],[70,30,0,2000,-1],[80,20,0,1000,-1]]",
            "number" : NumberInt(50)
        },
        "AG" : {
            "args" : "[[5,0,0,200],[20,0,0,100],[10,0,0,200],[10,0,0,400],[5,0,0,800],[20,0,0,2000,-1],[5,0,0,600],[100,70,0,100],[100,80,0,100],[100,80,0,200],[100,90,0,200],[100,90,0,1500],[100,70,0,600,-1],[100,80,0,900,-1],[95,5,0,100],[90,10,0,200],[100,0,0,800,-1],[100,0,0,2000,-1],[95,5,0,800,-1],[95,5,0,200],[100,0,0,700,-1],[80,20,0,500],[60,40,0,600],[80,20,0,400],[80,20,0,300],[70,30,0,1500,-1],[60,40,0,700],[60,10,0,200],[35,5,0,300],[50,20,0,400],[50,20,0,200],[50,0,0,200],[60,10,0,2000,-1],[50,0,0,2000,-1],[100,50,0,100],[100,60,0,100],[100,30,0,900,-1],[100,50,0,800,-1]]",
            "number" : NumberInt(15)
        },
        "AL" : {
            "args" : "[[20,0,0,2000],[20,0,0,300],[5,0,0,600],[5,0,0,2000],[10,0,0,300],[5,0,0,1500],[100,70,0,700],[100,70,0,1000],[100,90,0,2000,-1],[100,80,0,900,-1],[100,70,0,800],[100,70,0,1500],[95,5,0,300],[90,10,0,500],[100,0,0,700],[90,10,0,300],[95,5,0,2000,-1],[100,0,0,1500,-1],[95,5,0,500],[80,20,0,500],[60,40,0,700],[60,40,0,600],[60,40,0,800],[70,30,0,700],[70,30,0,200,-1],[80,20,0,400],[70,30,0,500],[70,30,0,800],[35,5,0,300],[35,5,0,1000],[50,0,0,1000],[60,10,0,600],[60,10,0,500],[100,30,0,600],[100,30,0,700],[100,50,0,1000],[100,60,0,200,-1],[100,30,0,200,-1],[100,30,0,400]]",
            "number" : NumberInt(10)
        },
        "AO" : {
            "args" : "[[10,0,0,700],[20,0,0,700],[20,0,0,600],[5,0,0,1000],[20,0,0,800],[20,0,0,1000],[100,70,0,300],[100,80,0,500],[100,70,0,400],[100,80,0,400],[100,70,0,500],[90,10,0,900],[100,0,0,800],[90,10,0,600],[90,10,0,500],[80,20,0,1000],[60,40,0,1500],[80,20,0,600],[70,30,0,900],[80,20,0,800],[60,40,0,500],[60,40,0,200,-1],[50,0,0,1000],[50,20,0,1000],[50,0,0,700],[35,5,0,800],[35,5,0,2000],[80,50,0,1000],[100,30,0,800],[80,50,0,900],[100,30,0,600],[100,50,0,300],[100,50,0,400]]",
            "number" : NumberInt(20)
        },
        "AP" : {
            "args" : "[[20,0,0,100],[20,0,0,400],[10,0,0,200],[5,0,0,200],[5,0,0,1500,-1],[20,0,0,2000,-1],[100,80,0,700],[100,80,0,800],[100,70,0,700],[100,70,0,2000,-1],[95,5,0,200],[90,10,0,200],[100,0,0,100],[90,10,0,600],[95,5,0,2000,-1],[90,10,0,900,-1],[60,40,0,400],[80,20,0,200],[60,40,0,1000],[80,20,0,300],[80,20,0,2000,-1],[80,20,0,1500,-1],[60,10,0,200],[35,5,0,200],[60,10,0,400],[50,20,0,300],[35,5,0,300],[50,20,0,800,-1],[80,50,0,500],[100,50,0,700],[100,50,0,100],[80,50,0,1000]]",
            "number" : NumberInt(16)
        },
        "AU" : {
            "args" : "[[5,0,0,200],[10,0,0,200],[20,0,0,200],[20,0,0,300],[10,0,0,300],[10,0,0,500],[5,0,0,500],[5,0,0,700],[100,80,0,900,-1],[100,80,0,700,-1],[100,80,0,800,-1],[90,10,0,400],[90,10,0,500],[100,0,0,100,-1],[90,10,0,1000,-1],[80,20,0,300],[60,40,0,1500,-1],[35,5,0,100],[35,5,0,1500],[35,5,0,200],[100,60,0,700,-1],[100,60,0,800,-1],[100,30,0,800,-1]]",
            "number" : NumberInt(4)
        },
        "B" : {
            "args" : "[[5,0,0,800],[10,0,0,400],[5,0,0,1000],[5,0,0,700],[5,0,0,200],[20,0,0,200],[20,0,0,300],[100,90,0,200,-1],[100,80,0,100,-1],[100,70,0,1000],[100,80,0,600,-1],[90,10,0,900],[100,0,0,400],[100,0,0,2000,-1],[100,0,0,1500,-1],[80,20,0,500],[80,20,0,1500],[70,30,0,600],[60,40,0,400,-1],[35,5,0,300],[35,5,0,700],[60,10,0,1500],[50,0,0,200],[60,10,0,1500],[50,0,0,1500,-1],[80,50,0,500],[80,50,0,600], [100, 50,0,200,-1],[100,30,0,200,-1],[100,50,0,1500,-1]]",
            "number" : NumberInt(50)
        },
        "BR" : {
            "args" : "[[5,0,0,1000],[10,0,0,800],[20,0,0,400],[20,0,0,500],[5,0,0,800],[10,0,0,700],[100,80,0,300],[100,80,0,400],[100,70,0,300],[100,70,0,500],[100,80,0,100,-1],[100,0,0,300],[95,5,0,400],[90,10,0,400],[95,5,0,300],[90,10,0,300],[100,0,0,1000,-1],[95,5,0,1500,-1],[80,20,0,500],[80,20,0,400],[80,20,0,300],[70,30,0,400],[60,40,0,700],[80,20,0,1500,-1],[50,20,0,400],[50,0,0,300],[60,10,0,400],[50,0,0,400], [60,10,0,100,-1],[100,30,0,300],[100,50,0,300],[100,60,0,400],[100,60,0,500],[100,30,0,400],[100,30,0,1500,-1],]",
            "number" : NumberInt(10)
        },
        "BU" : {
            "args" : "[[5,0,0,800],[5,0,0,600],[20,0,0,2000,-1],[10,0,0,600],[20,0,0,1500,-1],[100,90,0,500,-1],[100,70,0,2000,-1],[100,90,0,800,-1],[90,10,0,400],[95,5,0,1500,-1],[100,0,0,1500,-1],[100,0,0,2000,-1],[95,5,0,2000,-1],[90,10,0,500],[60,40,0,100,-1],[60,40,0,500,-1],[80,20,0,1000],[60,10,0,400],[50,0,0,300],[35,5,0,500],[50,0,0,1500,-1],[50,20,0,2000,-1],[50,0,0,2000,-1],[100,60,0,1500,-1],[100,30,0,600, -1],[100,60,0,2000,-1]]",
            "number" : NumberInt(16)
        },
        "C" : {
            "args" : "[[5,0,0,400],[10,0,0,900],[10,0,0,800],[10,0,0,200],[20,0,0,200],[20,0,0,300],[20,0,0,400],[20,0,0,600],[100,70,0,600],[100,70,0,200],[100,70,0,800],[100,90,0,700,-1],[95,5,0,100],[100,0,0,100],[90,10,0,100],[95,5,0,900],[100,0,0,200],[100,0,0,800],[90,10,0,500],[95,5,0,2000,-1],[90,10,0,300],[60,40,0,200],[60,40,0,400],[60,40,0,1500],[70,30,0,300],[60,40,0,600],[50,0,0,1000],[50,20,0,200],[50,0,0, 100],[50,0,0,800],[50,20,0,800],[100,30,0,100],[80,50,0,400],[80,50,0,500],[80,50,0,300],[80,50,0,900]]",
            "number" : NumberInt(30)
        },
        "CF" : {
            "args" : "[[5,0,0,300],[10,0,0,200],[5,0,0,400],[20,0,0,200],[10,0,0,300],[10,0,0,500],[100,70,0,900],[100,70,0,800],[100,70,0,700],[100,90,0,2000],[100,80,0,700],[100,70,0,200,-1],[95,5,0,100],[100,0,0,100],[90,10,0,300],[95,5,0,200],[90,10,0,200],[90,10,0,500],[95,5,0,300],[90,10,0,300],[90,10,0,100],[80,20,0,300],[80,20,0,600],[70,30,0,700],[60,40,0,900],[80,20,0,500],[50,20,0,2000],[60,10,0,1500],[60,10,0, 900],[60,10,0,300],[80,50,0,500],[100,50,0,500],[100,30,0,400],[100,30,0,700],[100,60,0,200,-1],[100,50,0,400]]",
            "number" : NumberInt(30)
        },
        "CJ" : {
            "args" : "[[5,0,0,2000,-1],[5,0,0,1500,-1],[20,0,0,400],[20,0,0,500],[5,0,0,900,-1],[100,80,0,100],[100,90,0,2000],[100,90,0,500],[100,90,0,400],[100,80,0,700],[90,10,0,900],[100,0,0,500],[90,10,0,400],[100,0,0,300,-1],[80,20,0,1000],[70,30,0,1000],[60,40,0,1500],[60,40,0,1000],[60,40,0,300,-1],[60,10,0,400],[60,10,0,1000],[50,0,0,1500,-1],[35,5,0,1500,-1],[100,60,0,100],[100,60,0,2000],[80,50,0,1500],[80,50,0 ,2000],[80,50,0,900],[100,30,0,700]]",
            "number" : NumberInt(16)
        },
        "CS" : {
            "args" : "[[10,0,0,2000],[20,0,0,1000],[5,0,0,300,-1],[20,0,0,900],[100,70,0,200],[100,80,0,200],[100,70,0,600],[100,70,0,400],[100,80,0,600],[90,10,0,200],[95,5,0,100],[95,5,0,200],[100,0,0,200],[60,40,0,300],[70,30,0,300],[60,40,0,400],[60,40,0,300],[70,30,0,200],[80,20,0,400],[60,10,0,200],[60,10,0,300],[50,20,0,200],[50,30,0,200],[50,20,0,400],[35,5,0,2000,-1],[80,50,0,200],[100,50,0,200],[80,50,0,300], [100,60,0,200],[100,30,0,200],[80,50,0,400],[100,50,0,1000,-1]]",
            "number" : NumberInt(50)
        },
        "CU" : {
            "args" : "[[10,0,0,600],[10,0,0,1500],[5,0,0,900],[10,0,0,700],[5,0,0,200],[5,0,0,400],[5,0,0,500],[100,90,0,2000],[100,70,0,1000],[100,80,0,1000],[100,80,0,900],[100,70,0,200],[90,10,0,100],[95,5,0,100],[90,10,0,1500,-1],[100,0,0,200],[60,40,0,400],[80,20,0,600],[70,30,0,2000,-1],[50,0,0,600],[50,0,0,700],[50,0,0,500],[100,60,0,200],[100,50,0,200],[80,50,0,2000,-1],[100,30,0,2000,-1]]",
            "number" : NumberInt(5)
        },
        "EB" : {
            "args" : "[[5,0,0,1000],[20,0,0,900],[20,0,0,200],[20,0,0,300],[20,0,0,800],[10,0,0,200],[5,0,0,300],[100,90,0,2000],[100,70,0,200],[100,70,0,800],[100,90,0,300,-1],[90,10,0,400],[90,10,0,100],[90,10,0,300],[90,10,0,200],[95,5,0,600],[100,0,0,1000,-1],[80,20,0,400],[60,40,0,500],[80,20,0,300],[60,40,0,2000,-1],[60,40,0,600],[80,20,0,200],[70,30,0,300],[50,20,0,400],[50,20,0,500],[60,10,0,500],[50,0,0,400], [60,10,0,400],[35,5,0,500],[50,20,0,300],[80,50,0,200],[80,50,0,300],[80,50,0,400],[100,60,0,1500,-1]]",
            "number" : NumberInt(16)
        },
        "EC" : {
            "args" : "[[20,0,0,800],[20,0,0,2000,-1],[20,0,0,500],[20,0,0,300],[20,0,0,400],[5,0,0,200],[5,0,0,500],[100,70,0,1000],[100,70,0,500],[100,80,0,1500],[90,10,0,300],[100,0,0,300],[95,5,0,200],[100,0,0,2000,-1],[60,40,0,500],[70,30,0,200],[60,40,0,1500,-1],[60,40,0,300],[70,30,0,400],[35,5,0,600],[60,10,0,300],[50,0,0,600],[50,0,0,2000,-1],[35,5,0,700],[50,20,0,600],[100,30,0,400],[80,50,0,500],[100,50,0,500], [80,50,0,300]]",
            "number" : NumberInt(6)
        },
        "EG" : {
            "args" : "[[20,0,0,300],[10,0,0,400,-1],[20,0,0,900,-1],[10,0,0,300,-1],[5,0,0,700,-1],[100,70,0,500],[100,70,0,800],[100,70,0,600],[100,70,0,2000],[90,10,0,300],[95,5,0,300],[95,5,0,200],[100,0,0,800,-1],[100,0,0,600,-1],[100,0,0,200],[90,10,0,200],[70,30,0,200],[70,30,0,300],[60,40,0,500],[80,20,0,2000,-1],[70,30,0,2000,-1],[35,5,0,200],[50,20,0,200],[60,10,0,100],[35,5,0,400],[35,5,0,600,-1],[100,60,0,500 ],[100,60,0,400],[100,60,0,800],[80,50,0,2000,-1],[100,30,0,400],[100,30,0,200]]",
            "number" : NumberInt(16)
        },
        "FG" : {
            "args" : "[[80,50,0,500],[80,50,0,300],[100,30,0,1500,-1],[100,50,0,2000,-1],[60,10,0,200],[50,0,0,300],[50,0,0,100,-1],[70,30,0,600],[60,40,0,200],[80,20,0,2000,-1],[60,40,0,400],[95,5,0,200],[100,0,0,2000,-1],[100,0,0,1500,-1],[90,10,0,400],[100,70,0,400],[100,70,0,2000,-1],[20,0,0,1500],[20,0,0,200,-1],[10,0,0,2000],[5,0,0,900,-1]]",
            "number" : NumberInt(50)
        },
        "FU" : {
            "args" : "[[5,0,0,1000],[5,0,0,700],[5,0,0,600],[20,0,0,2000,-1],[20,0,0,1000],[100,80,0,800],[100,70,0,300,-1],[100,90,0,300,-1],[100,80,0,400,-1],[100,80,0,200,-1],[100,0,0,1000],[100,0,0,800],[100,0,0,700],[100,0,0,100,-1],[90,10,0,200,-1],[95,5,0,100,-1],[100,0,0,400],[60,40,0,1000],[80,20,0,300,-1],[80,20,0,200,-1],[80,20,0,2000],[60,40,0,900],[50,0,0,1000],[50,0,0,900],[50,0,0,700],[50,0,0,600],[35,5,0, 1000],[60,10,0,200,-1],[50,0,0,100,-1],[100,50,0,800],[100,30,0,900],[80,50,0,200,-1],[100,30,0,100,-1],[100,60,0,900]]",
            "number" : NumberInt(10)
        },
        "HC" : {
            "args" : "[[90,10,0,700],[95,5,0,600],[100,0,0,2000,-1],[90,10,0,1500,-1],[70,30,0,600],[80,20,0,300,-1],[60,40,0,800],[60,40,0,1500,-1],[50,0,0,900],[100,60,0,200,-1],[50,0,0,800],[100,30,0,200,-1],[100,30,0,700],[10,0,0,700],[100,80,0,700],[100,70,0,1000,-1],[90,10,0,800]]",
            "number" : NumberInt(25)
        },
        "I" : {
            "args" : "[[90,10,0,400],[95,5,0,500],[60,40,0,900],[60,40,0,800],[60,40,0,700],[70,30,0,1000],[100,70,0,100],[20,0,0,200],[60,10,0,600],[50,0,0,200],[100,0,0,1500,-1],[95,5,0,1500,-1],[100,60,0,800,-1],[100,80,0,1000,-1],[100,90,0,2000,-1],[100,80,0,900,-1],[100,60,0,500,-1],[100,80,0,1500,-1],[100,60,0,500,-1]]",
            "number" : NumberInt(20)
        },
        "J" : {
            "args" : "[[100,70,0,400],[100,90,0,300,-1],[100,70,0,800],[100,90,0,700,-1],[95,5,0,200],[90,10,0,200],[100,0,0,500,-1],[100,0,0,2000,-1],[100,0,0,200],[70,30,0,300],[80,20,0,200],[80,20,0,2000,-1],[70,30,0,2000,-1],[60,40,0,300],[60,40,0,800],[50,20,0,200],[80,50,0,300],[100,30,0,300],[100,30,0,400],[80,50,0,1500,-1]]",
            "number" : NumberInt(4)
        },
        "JD" : {
            "args" : "[[5,0,0,400,-1],[5,0,0,800,-1],[10,0,0,200,-1],[20,0,0,800,-1],[10,0,0,2000,-1],[100,70,0,300],[100,70,0,200],[100,90,0,300],[100,90,0,1500],[100,80,0,200],[100,80,0,600],[100,80,0,500],[100,0,0,100],[90,10,0,200],[90,10,0,300],[100,0,0,600,-1],[60,40,0,300],[60,40,0,500],[70,30,0,600],[80,20,0,500],[80,20,0,1500,-1],[50,20,0,200],[50,20,0,500],[60,10,0,300],[50,20,0,400],[60,10,0,2000,-1],[35,5,0, 2000,-1],[100,50,0,200],[100,60,0,300],[100,60,0,200],[80,50,0,200],[100,50,0,300],[100,30,0,1500]]",
            "number" : NumberInt(25)
        },
        "JM" : {
            "args" : "[[100,70,0,1500],[100,80,0,2000],[90,10,0,300],[95,5,0,300],[90,10,0,800],[95,5,0,1500,-1],[90,10,0,1500,-1],[60,40,0,900],[70,30,0,900],[80,20,0,400],[80,20,0,2000,-1],[60,10,0,1000],[50,20,0,200],[60,10,0,700],[50,0,0,2000,-1],[80,50,0,700],[100,50,0,600],[10,0,0,800],[20,0,0,2000,-1]]",
            "number" : NumberInt(8)
        },
        "L" : {
            "args" : "[[20,0,0,100],[20,0,0,600,-1],[20,0,0,500,-1],[20,0,0,1000,-1],[10,0,0,600,-1],[5,0,0,800],[100,70,0,2000,-1],[100,70,0,300,-1],[100,80,0,1500,-1],[100,80,0,400,-1],[100,80,0,2000,-1],[90,10,0,500],[95,5,0,300],[95,5,0,400],[100,0,0,100,-1],[100,0,0,200,-1],[95,5,0,100,-1],[100,0,0,2000,-1],[95,5,0,100,-1],[60,40,0,300],[70,30,0,300],[80,20,0,100,-1],[70,30,0,200,-1],[80,20,0,200,-1],[80,20,0,900,-1],[60,10,0,900],[60,10,0,300],[60,10,0,1000],[50,0,0,200,-1],[50,20,0,200,-1],[80,50,0,500],[100,30,0,2000,-1],[100,60,0,2000,-1],[100,30,0,100,-1],[100,50,0,600,-1],[100,30,0,100,-1]]",
            "number" : NumberInt(16)
        },
        "LC" : {
            "args" : "[[20,0,0,900],[10,0,0,500],[20,0,0,1000],[20,0,0,400],[100,90,0,100],[100,70,0,100],[100,70,0,300],[100,80,0,300],[100,80,0,200],[100,90,0,400],[100,70,0,200],[95,5,0,100],[100,0,0,100],[90,10,0,500],[100,0,0,400],[90,10,0,900],[80,20,0,500],[70,30,0,600],[60,40,0,300],[70,30,0,300],[60,40,0,500],[70,30,0,900],[60,10,0,100],[60,10,0,500],[60,10,0,200],[60,10,0,1000],[50,20,0,900],[35,5,0,300,-1],[50,0,0,200,-1],[100,50,0,200],[100,50,0,300],[100,60,0,300],[100,30,0,300],[80,50,0,500],[80,50,0,400],[100,30,0,200]]",
            "number" : NumberInt(20)
        },
        "LH" : {
            "args" : "[[20,0,0,200],[20,0,0,300],[20,0,0,100],[10,0,0,300],[100,70,0,300],[100,80,0,300],[100,70,0,200],[100,80,0,400],[100,80,0,200],[100,90,0,1000],[100,70,0,400],[100,80,0,700],[90,10,0,200],[90,10,0,100],[90,10,0,200],[90,10,0,300],[100,0,0,1500],[90,10,0,800],[100,0,0,800],[80,20,0,200],[70,30,0,100],[60,40,0,300],[60,40,0,300],[70,30,0,300],[60,40,0,500],[70,30,0,1500],[60,10,0,300],[50,20,0,200], [50,0,0,100],[50,20,0,300],[50,20,0,400],[100,60,0,200],[80,50,0,200],[100,30,0,100],[100,60,0,300],[100,50,0,300],[100,60,0,100],[100,60,0,800]]",
            "number" : NumberInt(8)
        },
        "LU" : {
            "args" : "[[10,0,0,600],[10,0,0,700],[5,0,0,800],[20,0,0,1500,-1],[20,0,0,1000,-1],[10,0,0,800],[100,70,0,1500],[100,80,0,2000],[100,90,0,200,-1],[100,80,0,100,-1],[100,80,0,200,-1],[100,90,0,300,-1],[100,90,0,500],[100,0,0,700],[100,0,0,1000],[90,10,0,300,-1],[95,5,0,100,-1],[95,5,0,200,-1],[95,5,0,300,-1],[60,40,0,2000],[60,40,0,200,-1],[70,30,0,200,-1],[80,20,0,200,-1],[60,40,0,400,-1],[80,20,0,1500],[35,5,0,200],[50,20,0,300,-1],[50,20,0,400,-1],[50,20,0,2000,-1],[60,10,0,1500,-1],[100,30,0,1000],[100,30,0,1500],[100,50,0,2000],[100,60,0,2000],[80,50,0,300,-1],[100,50,0,200,-1],[100,60,0,300,-1]]",
            "number" : NumberInt(6)
        },
        "M" : {
            "args" : "[[5,0,0,1500],[5,0,0,2000],[20,0,0,200],[20,0,0,400],[20,0,0,600],[10,0,0,400],[5,0,0,600],[5,0,0,1000],[10,0,0,500],[100,80,0,1500],[100,70,0,1500],[100,70,0,600,-1],[100,90,0,800,-1],[90,10,0,500],[90,10,0,600],[100,0,0,100,-1],[80,20,0,600],[70,30,0,200,-1],[70,30,0,1500],[35,5,0,2000],[35,5,0,1500],[35,5,0,200],[35,5,0,300],[50,0,0,200],[80,50,0,700],[100,50,0,300,-1],[100,60,0,200,-1],[100,30,0, 100,-1]]",
            "number" : NumberInt(50)
        },
        "MA" : {
            "args" : "[[20,0,0,700],[20,0,0,300],[20,0,0,600],[20,0,0,900],[20,0,0,1000],[20,0,0,2000],[5,0,0,1000,-1],[100,70,0,700],[100,80,0,600],[100,70,0,1000],[100,70,0,800],[100,70,0,2000,-1],[100,0,0,700],[100,0,0,800],[95,5,0,400],[90,10,0,800],[90,10,0,2000,-1],[100,0,0,300],[100,0,0,400],[80,20,0,200],[80,20,0,400],[60,40,0,800],[60,40,0,700],[80,20,0,500],[80,20,0,300],[80,20,0,2000,-1],[60,40,0,300],[60,40,0,600],[50,0,0,300],[50,20,0,300],[50,0,0,200],[50,0,0,600],[50,0,0,900],[50,20,0,2000,-1],[35,5,0,600],[35,5,0,400],[80,50,0,300],[80,50,0,200],[100,60,0,800],[80,50,0,800],[80,50,0,900],[80,50,0,1000],[100,60,0,1500],[100,30,0,1500,-1]]",
            "number" : NumberInt(35)
        },
        "NI" : {
            "args" : "[[20,0,0,2000],[10,0,0,1500],[10,0,0,2000],[20,0,0,200,-1],[10,0,0,600,-1],[100,80,0,200],[100,70,0,2000],[100,70,0,400,-1],[100,80,0,800,-1],[100,80,0,700,-1],[100,0,0,1500],[90,10,0,2000],[100,0,0,800],[100,0,0,400,-1],[95,5,0,300,-1],[80,20,0,2000],[70,30,0,1500],[60,40,0,400,-1],[60,40,0,1500],[80,20,0,1500],[35,5,0,2000],[50,0,0,1500],[50,0,0,2000],[50,0,0,700],[35,5,0,1500],[60,10,0,100,-1], [50,0,0,200,-1],[100,30,0,1500],[80,50,0,400],[80,50,0,300],[100,30,0,900],[100,30,0,400,-1],[100,30,0,300,-1],[100,30,0,200]]",
            "number" : NumberInt(8)
        },
        "OI" : {
            "args" : "[[20,0,0,200],[10,0,0,200],[10,0,0,300],[10,0,0,400],[5,0,0,500],[20,0,0,300],[100,70,0,1000],[100,70,0,2000,-1],[100,90,0,500,-1],[100,70,0,100,-1],[100,90,0,300,-1],[100,0,0,1000],[100,0,0,800],[90,10,0,500,-1],[90,10,0,200,-1],[90,10,0,1000],[60,40,0,500],[70,30,0,1500],[70,30,0,700,-1],[80,20,0,600,-1],[60,40,0,400],[35,5,0,100],[60,10,0,100],[50,20,0,300],[50,0,0,900],[50,20,0,1000],[50,0,0,200 ],[60,10,0,800,-1],[80,50,0,500],[100,60,0,100,-1],[100,50,0,2000,-1],[100,30,0,500,-1],[100,30,0,1500]]",
            "number" : NumberInt(16)
        },
        "P" : {
            "args" : "[[10,0,0,1500],[5,0,0,1500],[5,0,0,1000],[5,0,0,600],[20,0,0,600],[20,0,0,800],[10,0,0,400],[10,0,0,600],[5,0,0,800],[5,0,0,700],[20,0,0,2000],[100,80,0,200],[100,70,0,500],[100,80,0,400,-1],[100,80,0,1000,-1],[90,10,0,300],[95,5,0,200],[95,5,0,300],[100,0,0,300],[90,10,0,800,-1],[60,40,0,1500],[60,40,0,600],[80,20,0,1000],[80,20,0,1500],[80,20,0,600,-1],[50,0,0,300],[35,5,0,300],[35,5,0,800],[50,20,0,1000],[60,10,0,700],[35,5,0,900],[50,20,0,300],[50,20,0,800],[50,20,0,1000],[50,0,0,500],[100,30,0,300],[100,30,0,1500],[100,30,0,500],[80,50,0,400],[100,50,0,1000,-1],[100,30,0,900,-1]]",
            "number" : NumberInt(12)
        },
        "PB" : {
            "args" : "[[10,0,0,500],[10,0,0,1500],[10,0,0,700],[10,0,0,600],[10,0,0,1000],[20,0,0,100,-1],[100,70,0,2000],[100,90,0,100,-1],[100,70,0,100,-1],[100,90,0,400,-1],[100,80,0,400,-1],[100,0,0,500],[90,10,0,800],[95,5,0,900],[90,10,0,200,-1],[100,0,0,200,-1],[95,5,0,2000,-1],[80,20,0,900],[70,30,0,900],[80,20,0,200,-1],[70,30,0,200,-1],[60,40,0,500,-1],[70,30,0,300,-1],[70,30,0,800],[50,0,0,1500],[50,0,0,500],[50,0,0,900],[35,5,0,1500],[60,10,0,200,-1],[50,20,0,400,-1],[50,0,0,600],[80,50,0,900],[80,50,0,1000],[100,60,0,2000],[80,50,0,200,-1],[100,60,0,200,-1],[100,30,0,200,-1],[80,50,0,2000,-1]]",
            "number" : NumberInt(12)
        },
        "PG" : {
            "args" : "[[20,0,0,500],[20,0,0,400],[20,0,0,600],[20,0,0,200],[5,0,0,1000,-1],[10,0,0,1500,-1],[100,80,0,400],[100,70,0,700],[100,80,0,1500],[100,70,0,100,-1],[100,80,0,100,-1],[100,70,0,2000,-1],[100,0,0,400],[90,10,0,400,-1],[100,0,0,700],[100,0,0,300,-1],[95,5,0,900],[60,40,0,200,-1],[70,30,0,200,-1],[80,20,0,2000,-1],[60,40,0,900],[70,30,0,1500],[60,40,0,1000],[50,0,0,500],[50,0,0,400],[50,20,0,400,-1], [60,10,0,400,-1],[50,0,0,1500,-1],[60,10,0,1000,-1],[50,0,0,700],[100,50,0,600],[100,30,0,400],[100,50,0,400],[100,50,0,500],[80,50,0,1500,-1],[80,50,0,300,-1],[100,50,0,100,-1],[100,60,0,2000,-1]]",
            "number" : NumberInt(6)
        },
        "PK" : {
            "args" : "[[5,0,0,1000],[5,0,0,800],[10,0,0,1500,-1],[20,0,0,1500,-1],[20,0,0,300,-1],[5,0,0,900],[100,80,0,900],[100,80,0,800],[100,70,0,800],[100,70,0,600],[100,90,0,800],[100,70,0,1500,-1],[100,0,0,2000],[90,10,0,800],[95,5,0,200],[100,0,0,200],[100,0,0,100,-1],[90,10,0,100,-1],[95,5,0,400],[80,20,0,500],[60,40,0,400],[60,40,0,500],[50,20,0,400],[50,20,0,500],[50,20,0,300],[50,20,0,900,-1],[50,0,0,700,-1], [100,60,0,800],[100,50,0,600],[100,60,0,400],[100,50,0,700],[100,50,0,800],[100,30,0,600],[100,30,0,1500,-1],[100,30,0,100,-1]]",
            "number" : NumberInt(16)
        },
        "PP" : {
            "args" : "[[20,0,0,100],[5,0,0,800],[5,0,0,700],[10,0,0,400],[20,0,0,500,-1],[5,0,0,900],[100,70,0,800],[100,70,0,900],[100,70,0,700],[100,80,0,1000],[100,90,0,500,-1],[100,70,0,1500,-1],[90,10,0,700],[100,0,0,400],[95,5,0,800],[100,0,0,2000,-1],[95,5,0,2000,-1],[90,10,0,2000,-1],[95,5,0,1500,-1],[95,5,0,200],[60,40,0,300],[60,40,0,400],[60,40,0,200,-1],[80,30,0,800,-1],[60,40,0,1000,-1],[70,30,0,600,-1],[35,5,0,800],[60,10,0,700],[50,0,0,600],[50,0,0,1000],[60,10,0,2000,-1],[60,10,0,200,-1],[50,20,0,500],[50,20,0,400],[80,50,0,500],[80,50,0,400],[100,30,0,300],[100,60,0,800],[80,50,0,300],[100,50,0,2000,-1],[80,50,0,2000,-1],[100,60,0,1500,-1]]",
            "number" : NumberInt(16)
        },
        "PX" : {
            "args" : "[[20,0,0,1000],[20,0,0,500],[20,0,0,400],[5,0,0,300,-1],[5,0,0,700,-1],[100,90,0,800],[100,90,0,700],[100,90,0,500],[100,70,0,200,-1],[90,10,0,700],[90,10,0,100],[90,10,0,2000],[90,10,0,400,-1],[90,10,0,800],[90,10,0,200],[80,20,0,100],[80,20,0,200],[80,20,0,300],[60,40,0,200],[70,30,0,500,-1],[70,30,0,1000],[80,20,0,1000],[60,10,0,100],[50,20,0,200],[60,10,0,500],[50,20,0,500],[50,20,0,300], [50,0,0,100],[35,5,0,1500,-1],[60,10,0,900,-1],[100,60,0,100],[100,60,0,800],[100,60,0,1000],[100,30,0,200],[100,50,0,600],[100,60,0,900]]",
            "number" : NumberInt(7)
        },
        "RB" : {
            "args" : "[[80,50,0,600],[80,50,0,700],[60,10,0,600],[60,10,0,700],[70,30,0,900],[60,40,0,800],[60,40,0,1000],[90,10,0,700],[90,10,0,600],[100,70,0,400],[20,0,0,200],[100,80,0,1500,-1],[100,60,0,700,-1],[80,20,0,2000,-1],[70,30,0,2000,-1],[90,10,0,1500,-1],[100,30,0,1000,-1],[100,0,0,1000,-1],[90,10,0,2000,-1]]",
            "number" : NumberInt(25)
        },
        "RM" : {
            "args" : "[[20,0,0,700],[10,0,0,1500],[10,0,0,1000],[20,0,0,500],[20,0,0,800],[20,0,0,900],[100,70,0,1500],[100,80,0,100,-1],[100,90,0,500,-1],[100,90,0,300,-1],[100,90,0,700,-1],[100,90,0,800,-1],[100,90,0,900,-1],[100,90,0,1000,-1],[100,80,0,600,-1],[100,80,0,700,-1],[100,80,0,900,-1],[90,10,0,800],[90,10,0,400],[95,5,0,100,-1],[100,0,0,500],[90,10,0,300],[60,40,0,700],[80,20,0,300],[80,20,0,400],[60,40,0,1000,-1],[70,30,0,2000,-1],[60,40,0,2000,-1],[50,0,0,1000],[35,5,0,800],[50,0,0,900],[60,10,0,200,-1],[50,0,0,800],[35,5,0,1500],[50,20,0,600,-1],[100,60,0,2000],[100,50,0,300,-1],[80,50,0,200,-1],[80,50,0,300,-1],[100,50,0,200,-1],[100,50,0,300,-1],[100,30,0,200,-1]]",
            "number" : NumberInt(50)
        },
        "RU" : {
            "args" : "[[20,0,0,700],[20,0,0,300],[10,0,0,100],[100,90,0,500],[100,90,0,600],[100,90,0,700],[100,90,0,800],[100,90,0,900],[100,90,0,1000],[100,90,0,1500],[100,90,0,100,-1],[100,70,0,600],[100,80,0,1000],[90,10,0,500],[100,0,0,700,-1],[90,10,0,300],[90,10,0,200],[80,20,0,300],[80,20,0,200],[70,30,0,1000],[60,40,0,1000],[60,10,0,200],[60,10,0,300],[50,20,0,1000],[100,60,0,800],[100,50,0,600],[100,60,0,900],[100,50,0,800],[100,30,0,500,-1],[100,60,0,1000],[100,60,0,600]]",
            "number" : NumberInt(11)
        },
        "SA" : {
            "args" : "[[20,0,0,200],[100,70,0,300],[100,70,0,800],[100,90,0,2000,-1],[100,70,0,900],[90,10,0,200],[90,10,0,300],[95,5,0,400],[100,0,0,2000,-1],[95,5,0,2000,-1],[90,10,0,2000,-1],[90,10,0,400],[70,30,0,300],[80,20,0,300],[60,40,0,500],[70,30,0,700],[60,10,0,300],[60,10,0,1000],[100,30,0,300],[100,30,0,2000,-1],[100,30,0,1500,-1],[80,50,0,600]]",
            "number" : NumberInt(25)
        },
        "SC" : {
            "args" : "[[10,0,0,200],[10,0,0,300],[5,0,0,300],[5,0,0,800],[5,0,0,500],[5,0,0,600],[20,0,0,200],[20,0,0,700],[20,0,0,2000,-1],[10,0,0,900],[20,0,0,600],[100,70,0,900],[100,80,0,2000],[100,70,0,1000],[100,80,0,400,-1],[100,0,0,700],[95,5,0,200,-1],[90,10,0,600,-1],[95,0,0,100],[80,20,0,2000],[80,20,0,500,-1],[60,10,0,100],[50,20,0,100],[50,10,0,1000],[60,10,0,300],[60,10,0,200],[100,60,0,900],[80,50,0,200],[100,60,0,800],[100,30,0,500]]",
            "number" : NumberInt(3)
        },
        "SF" : {
            "args" : "[[10,0,0,900,-1],[10,0,0,800,-1],[10,0,0,500,-1],[20,0,0,200],[100,70,0,300],[100,80,0,200],[100,90,0,900],[100,70,0,1500,-1],[90,10,0,200],[95,5,0,100],[95,5,0,200],[100,0,0,100],[100,0,0,1500,-1],[100,0,0,400,-1],[80,20,0,200],[80,20,0,100],[60,40,0,400],[80,20,0,1500,-1],[60,40,0,2000,-1],[50,20,0,200],[60,10,0,900,-1],[80,50,0,200],[100,60,0,100],[100,50,0,200],[100,50,0,1000,-1]]",
            "number" : NumberInt(35)
        },
        "SH" : {
            "args" : "[[5,0,0,800],[20,0,0,900],[5,0,0,700],[10,0,0,900],[20,0,0,1500,-1],[100,90,0,600],[100,70,0,700],[100,90,0,400],[100,90,0,500],[100,80,0,900],[100,80,0,1000],[100,80,0,1500],[90,10,0,100],[95,5,0,400],[100,0,0,2000,-1],[70,30,0,200],[70,30,0,300],[80,20,0,300],[60,10,0,100],[50,20,0,200],[60,10,0,400],[35,5,0,1500,-1],[100,50,0,400],[100,60,0,600],[100,50,0,600],[100,30,0,600],[100,50,0,700],[100, 30,0,2000,-1]]",
            "number" : NumberInt(7)
        },
        "SI" : {
            "args" : "[[20,0,0,500],[20,0,0,400],[20,0,0,800],[5,0,0,1500,-1],[20,0,0,1500],[20,0,0,600],[20,0,0,700],[100,80,0,500],[100,90,0,500],[100,80,0,400],[100,90,0,700],[100,70,0,500],[100,80,0,1000],[100,70,0,400],[100,80,0,600],[90,10,0,500],[95,5,0,200],[100,0,0,200],[90,10,0,200],[90,10,0,600],[95,5,0,1500,-1],[80,20,0,600],[70,30,0,700],[70,30,0,800],[60,10,0,500],[60,10,0,800],[50,20,0,600],[50,20,0,700],[50,20,0,1000],[80,50,0,600],[100,60,0,400],[100,50,0,500],[100,60,0,500],[80,50,0,500],[80,50,0,700],[100,60,0,700],[100,30,0,300],[100,30,0,500]]",
            "number" : NumberInt(30)
        },
        "SM" : {
            "args" : "[[20,0,0,200],[20,0,0,300],[20,0,0,900,-1],[20,0,0,500],[10,0,0,500],[100,70,0,200],[100,70,0,300],[100,70,0,400],[100,70,0,600],[100,80,0,500],[100,70,0,500],[100,0,0,100],[90,10,0,200],[100,0,0,200],[95,5,0,300],[90,10,0,300],[100,0,0,800,-1],[100,0,0,1000,-1],[60,40,0,300],[80,20,0,200],[60,40,0,400],[70,30,0,300],[80,20,0,300],[60,40,0,2000,-1],[50,20,0,200],[60,10,0,200],[50,0,0,200],[50,20, 0,1500,-1],[50,0,0,300],[80,50,0,400],[100,30,0,200],[100,60,0,200],[100,50,0,400],[100,60,0,300],[100,30,0,1500,-1],[100,50,0,200]]",
            "number" : NumberInt(35)
        },
        "SN" : {
            "args" : "[[20,0,0,1500],[20,0,0,2000],[10,0,0,1500],[10,0,0,900],[5,0,0,1500],[20,0,0,1000],[100,70,0,2000],[100,70,0,600,-1],[100,70,0,700,-1],[90,10,0,1500],[95,5,0,1500],[90,10,0,1000],[95,5,0,500],[95,5,0,400],[90,10,0,700],[80,20,0,1500],[70,30,0,2000],[60,40,0,2000],[60,40,0,600],[35,5,0,900],[50,0,0,800],[60,10,0,1500],[50,0,0,1000],[60,10,0,500],[35,5,0,800],[50,20,0,500],[50,0,0,400],[50,0,0,500], [50,0,0,600],[100,50,0,2000],[80,50,0,800],[80,50,0,1000],[100,60,0,500,-1],[100,60,0,600,-1],[100,60,0,700,-1],[100,30,0,800]]",
            "number" : NumberInt(12)
        },
        "SP" : {
            "args" : "[[20,0,0,800],[20,0,0,400],[20,0,0,900],[10,0,0,400],[10,0,0,500],[5,0,0,800],[5,0,0,1000],[100,70,0,500],[100,70,0,2000],[100,70,0,700],[100,70,0,1500],[100,70,0,300],[100,90,0,400,-1],[100,90,0,1500],[100,80,0,900],[95,5,0,900],[100,0,0,600],[100,0,0,700],[95,5,0,100,-1],[95,5,0,200],[70,30,0,1500],[80,20,0,300],[70,30,0,1000],[35,5,0,900],[50,0,0,1500],[50,0,0,900],[50,0,0,1000],[60,10,0, 2000,-1],[50,0,0,800],[35,5,0,1000],[100,30,0,700],[100,50,0,2000],[80,50,0,800]]",
            "number" : NumberInt(16)
        },
        "SR" : {
            "args" : "[[5,0,0,1500],[20,0,0,1000],[20,0,0,400],[20,0,0,800],[20,0,0,1000],[10,0,0,1000],[10,0,0,1500],[5,0,0,2000],[100,70,0,1000,-1],[100,80,0,1000,-1],[100,80,0,1500,-1],[100,70,0,200],[100,70,0,500,-1],[100,70,0,1500,-1],[90,10,0,1500],[90,10,0,400],[100,0,0,200,-1],[90,10,0,1500],[80,20,0,2000],[70,30,0,2000],[80,20,0,500,-1],[70,30,0,500,-1],[60,40,0,500],[60,10,0,200],[50,0,0,200],[35,5,0,300],[50,20,0,1000,-1],[80,50,0,1000],[100,30,0,300,-1],[100,60,0,1500,-1],[100,60,0,800,-1],[100,60,0,900,-1],[100,30,0,1000,-1],[100,50,0,500,-1],]",
            "number" : NumberInt(16)
        },
        "T" : {
            "args" : "[[20,0,0,200],[20,0,0,100],[20,0,0,300],[20,0,0,600],[20,0,0,700],[10,0,0,300],[10,0,0,500],[5,0,0,500],[5,0,0,800],[5,0,0,900],[20,0,0,1500,-1],[100,70,0,2000],[100,90,0,500,-1],[100,90,0,300,-1],[100,90,0,400,-1],[100,70,0,1000],[100,90,0,200,-1],[90,10,0,500],[95,5,0,500],[90,10,0,300],[90,10,0,600],[70,30,0,800],[80,20,0,500],[80,20,0,700],[70,30,0,700],[60,40,0,900],[60,40,0,1000],[50,20,0,600],[50,20,0,700],[50,20,0,500],[50,20,0,800],[50,0,0,200],[50,0,0,600],[50,0,0,500],[100,70,0,300],[80,50,0,1000],[80,50,0,800],[100,30,0,100],[80,50,0,700]]",
            "number" : NumberInt(5)
        },
        "TA" : {
            "args" : "[[20,0,0,700],[20,0,0,200],[20,0,0,300],[20,0,0,400],[20,0,0,600],[20,0,0,2000,-1],[20,0,0,1500,-1],[20,0,0,800],[10,0,0,500],[100,70,0,300],[100,90,0,600],[100,90,0,800],[100,80,0,400],[100,80,0,2000,-1],[100,70,0,400],[90,10,0,800],[100,0,0,400],[100,0,0,200],[100,0,0,2000,-1],[100,0,0,300],[95,5,0,400],[95,5,0,800],[60,40,0,800],[70,30,0,800],[70,30,0,1000],[70,30,0,700],[60,40,0,1500],[80,20,0,1000],[35,5,0,400],[35,5,0,600],[50,0,0,300],[50,20,0,600],[50,0,0,1500,-1],[50,20,0,200],[60,10,0,900],[80,50,0,200],[100,30,0,200],[80,50,0,1000],[100,60,0,1000,-1],[80,50,0,1500],[100,60,0,200]]",
            "number" : NumberInt(36)
        },
        "TF" : {
            "args" : "[[20,0,0,200],[20,0,0,400],[20,0,0,2000,-1],[10,0,0,500],[5,0,0,200],[100,70,0,800],[100,80,0,200,-1],[100,70,0,100,-1],[100,90,0,500,-1],[100,90,0,600,-1],[90,10,0,500],[100,0,0,200],[100,0,0,300],[100,0,0,400],[100,0,0,1500,-1],[90,10,0,300],[95,5,0,500],[90,10,0,600],[70,30,0,600],[60,40,0,700],[80,20,0,300],[80,20,0,400],[80,20,0,500],[80,20,0,700],[70,30,0,300],[60,10,0,400],[60,10,0,300],[50,20,0,300],[50,20,0,600],[50,20,0,200],[35,5,0,400],[35,5,0,600],[35,5,0,700],[35,5,0,800],[80,50,0,1000],[80,50,0,800],[80,50,0,900],[100,60,0,200,-1],[100,30,0,500],[100,30,0,600],[100,30,0,2000,-1]]",
            "number" : NumberInt(10)
        },
        "TL" : {
            "args" : "[[10,0,0,100],[20,0,0,100],[20,0,0,300],[10,0,0,200],[10,0,0,300],[10,0,0,700],[5,0,0,500],[5,0,0,400],[100,70,0,200],[100,70,0,300],[100,90,0,1500,-1],[100,90,0,500],[100,70,0,2000,-1],[100,0,0,300],[100,0,0,200],[100,0,0,700,-1],[100,0,0,900,-1],[90,10,0,400],[90,10,0,500],[90,10,0,200],[80,20,0,300],[80,20,0,400],[80,20,0,600],[60,40,0,1500,-1],[70,30,0,800],[60,40,0,300],[60,40,0,400],[60,40,0,500],[60,40,0,700],[50,0,0,100],[50,20,0,200],[50,20,0,300],[50,20,0,600],[50,0,0,500],[80,50,0,300],[80,50,0,600],[80,50,0,500],[100,60,0,200],[100,30,0,1500,-1],[100,50,0,300],[100,30,0,200]]",
            "number" : NumberInt(3)
        },
        "TS" : {
            "args" : "[[20,0,0,200],[10,0,0,200],[20,0,0,500],[20,0,0,700],[10,0,0,600],[10,0,0,800],[100,70,0,600],[100,80,0,600],[100,90,0,300,-1],[100,80,0,1000],[100,80,0,500],[100,80,0,900],[90,10,0,600],[95,5,0,100],[100,0,0,500],[95,5,0,500],[90,10,0,700],[90,10,0,300],[90,10,0,500],[95,5,0,300],[95,5,0,2000,-1],[70,30,0,200],[80,20,0,200],[80,20,0,700],[60,40,0,800],[80,20,0,600],[60,40,0,700],[60,40,0,500],[60,40,0,600],[60,40,0,1000],[60,10,0,600],[50,20,0,400],[50,20,0,600],[50,20,0,700],[50,20,0,1000],[35,5,0,300],[50,0,0,700],[50,0,0,800],[60,10,0,600],[100,30,0,700],[100,30,0,200],[100,30,0,800],[100,60,0,400],[100,50,0,800]]",
            "number" : NumberInt(10)
        },
        "UR" : {
            "args" : "[[20,0,0,800],[20,0,0,200],[20,0,0,700],[20,0,0,900],[20,0,0,500],[10,0,0,900],[10,0,0,2000],[100,90,0,1500,-1],[100,80,0,1000,-1],[100,70,0,700,-1],[100,90,0,500,-1],[95,5,0,100],[95,5,0,200],[90,10,0,100],[90,10,0,200],[100,0,0,1500,-1],[100,0,0,600,-1],[90,10,0,800,-1],[60,40,0,400],[80,20,0,200],[70,30,0,300],[60,40,0,500],[60,40,0,300],[70,30,0,400],[80,20,0,2000,-1],[80,20,0,1000,-1],[80,20,0,400],[70,30,0,500],[35,5,0,300],[50,0,0,200],[60,10,0,200],[50,20,0,200],[35,5,0,400],[60,10,0,300],[35,5,0,500],[35,5,0,600],[60,10,0,1000],[80,50,0,500],[80,50,0,600],[80,50,0,2000,-1],[100,50,0,1500,-1],[100,30,0,1000,-1],[100,30,0,1500,-1],[100,50,0,1500,-1]]",
            "number" : NumberInt(25)
        },
        "V" : {
            "args" : "[[20,0,0,300],[20,0,0,200],[20,0,0,400],[20,0,0,600],[20,0,0,700],[20,0,0,800],[100,70,0,700],[100,80,0,700],[100,90,0,800],[100,70,0,900],[100,80,0,500],[100,80,0,600],[100,70,0,600],[100,80,0,900],[95,5,0,100],[90,10,0,100],[95,5,0,500],[90,10,0,600],[90,10,0,800],[90,10,0,700],[90,10,0,400],[90,10,0,900],[100,0,0,600],[80,20,0,300],[80,20,0,400],[80,20,0,700],[70,30,0,500],[70,30,0,200],[80,20,0,600],[60,40,0,1000],[60,10,0,100],[60,10,0,200],[60,10,0,600],[50,20,0,600],[50,20,0,800],[35,5,0,400],[35,5,0,700],[35,5,0,1500,-1],[50,0,0,1000,-1],[80,50,0,200],[80,50,0,400],[80,50,0,600],[100,50,0,600],[100,60,0,700],[100,50,0,800]]",
            "number" : NumberInt(25)
        },
        "Y" : {
            "args" : "[[10,0,0,400],[20,0,0,300],[20,0,0,400],[20,0,0,800],[10,0,0,300],[10,0,0,500],[10,0,0,600],[10,0,0,800],[5,0,0,1500],[5,0,0,1000],[100,70,0,500],[100,90,0,800,-1],[100,90,0,1000,-1],[100,90,0,600,-1],[100,90,0,900,-1],[90,10,0,1000],[100,0,0,600,-1],[90,10,0,700,-1],[10,0,0,400],[80,20,0,400],[80,20,0,900,-1],[70,30,0,700],[70,30,0,600],[70,30,0,400],[60,40,0,800],[60,40,0,600],[60,40,0,700],[35,5,0,600],[50,20,0,200],[50,20,0,500],[50,20,0,1000],[60,10,0,800,-1],[50,0,0,800],[50,0,0,1000],[50,0,0,900],[50,0,0,600],[35,5,0,1000],[80,50,0,400],[80,50,0,500],[100,30,0,100,-1],[100,50,0,800,-1],[100,60,0,200,-1]]",
            "number" : NumberInt(12)
        },
        "ZN" : {
            "args" : "[[5,0,0,500],[20,0,0,300],[20,0,0,1500],[10,0,0,300],[10,0,0,400],[10,0,0,500],[10,0,0,600],[5,0,0,600],[5,0,0,700],[5,0,0,1000],[100,70,0,300,-1],[100,80,0,200,-1],[100,80,0,500,-1],[100,90,0,1000,-1],[100,80,0,400,-1],[100,90,0,800,-1],[100,90,0,900,-1],[100,0,0,1500],[90,10,0,600,-1],[80,20,0,900,-1],[80,20,0,1000,-1],[60,40,0,900],[35,5,0,500],[35,5,0,400],[35,5,0,1500],[35,5,0,2000],[35,5,0,600],[100,30,0,1500],[100,60,0,200,-1],[100,60,0,300,-1],[100,60,0,1000,-1],[100,60,0,800,-1]]",
            "number" : NumberInt(15)
        }
    }
}