#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MongoDB数据复制脚本
将远程MongoDB服务器的Product数据复制到本地MongoDB服务器
"""

from pymongo import MongoClient
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
from bson import ObjectId

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('product_data_update.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MongoDataMigrator:
    """MongoDB数据迁移类"""

    def __init__(self, source_config: Dict[str, Any], target_config: Dict[str, Any]):
        """
        初始化MongoDB连接配置

        Args:
            source_config: 源数据库配置
            target_config: 目标数据库配置
        """
        self.source_config = source_config
        self.target_config = target_config
        self.source_client = None
        self.target_client = None

    def connect(self) -> bool:
        """
        连接到源和目标MongoDB数据库

        Returns:
            bool: 连接成功返回True，否则返回False
        """
        try:
            # 连接源数据库
            source_uri = f"mongodb://{self.source_config['host']}:{self.source_config['port']}"
            self.source_client = MongoClient(source_uri, serverSelectionTimeoutMS=5000)
            self.source_client.server_info()  # 测试连接
            logger.info(f"成功连接到源数据库: {source_uri}")

            # 连接目标数据库
            target_uri = f"mongodb://{self.target_config['host']}:{self.target_config['port']}"
            self.target_client = MongoClient(target_uri, serverSelectionTimeoutMS=5000)
            self.target_client.server_info()  # 测试连接
            logger.info(f"成功连接到目标数据库: {target_uri}")

            return True

        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False

    def get_collection_stats(self, client: MongoClient, db_name: str, collection_name: str) -> Optional[Dict]:
        """
        获取集合统计信息

        Args:
            client: MongoDB客户端
            db_name: 数据库名称
            collection_name: 集合名称

        Returns:
            Dict: 集合统计信息
        """
        try:
            db = client[db_name]
            collection = db[collection_name]
            stats = {
                'count': collection.count_documents({}),
                'exists': collection_name in db.list_collection_names()
            }
            return stats
        except Exception as e:
            logger.error(f"获取集合统计信息失败: {str(e)}")
            return None

    def get_document_last_date(self, document: Dict) -> Optional[datetime]:
        """
        获取文档value数组中最后一条数据的日期

        Args:
            document: 文档数据

        Returns:
            datetime: 最后一条数据的日期，如果没有则返回None
        """
        try:
            if 'value' in document and isinstance(document['value'], list) and len(document['value']) > 0:
                last_item = document['value'][-1]
                if 'date' in last_item:
                    return last_item['date']
            return None
        except Exception as e:
            logger.error(f"获取文档最后日期失败: {str(e)}")
            return None

    def compare_document_dates(self, source_doc: Dict, target_doc: Dict) -> bool:
        """
        比较源文档和目标文档的最后日期

        Args:
            source_doc: 源文档
            target_doc: 目标文档

        Returns:
            bool: 如果源文档更新则返回True，否则返回False
        """
        source_last_date = self.get_document_last_date(source_doc)
        target_last_date = self.get_document_last_date(target_doc)

        # 如果源文档没有日期，跳过
        if source_last_date is None:
            return False

        # 如果目标文档没有日期，需要更新
        if target_last_date is None:
            return True

        # 比较日期
        return source_last_date > target_last_date

    def get_newer_values(self, source_doc: Dict, target_doc: Dict) -> List[Dict]:
        """
        获取源文档中比目标文档更新的value数据

        Args:
            source_doc: 源文档
            target_doc: 目标文档

        Returns:
            List[Dict]: 需要更新的value数据列表
        """
        try:
            target_last_date = self.get_document_last_date(target_doc)

            if 'value' not in source_doc or not isinstance(source_doc['value'], list):
                return []

            newer_values = []

            for value_item in source_doc['value']:
                if 'date' in value_item:
                    # 如果目标文档没有日期，或者源数据日期更新
                    if target_last_date is None or value_item['date'] > target_last_date:
                        newer_values.append(value_item)

            return newer_values
        except Exception as e:
            logger.error(f"获取更新数据失败: {str(e)}")
            return []

    def incremental_sync_collection(self, source_db: str, source_collection: str,
                                   target_db: str, target_collection: str,
                                   batch_size: int = 100) -> bool:
        """
        增量同步集合数据 - 只更新有新数据的文档

        Args:
            source_db: 源数据库名称
            source_collection: 源集合名称
            target_db: 目标数据库名称
            target_collection: 目标集合名称
            batch_size: 批处理大小

        Returns:
            bool: 同步成功返回True，否则返回False
        """
        try:
            # 获取源和目标集合
            source_coll = self.source_client[source_db][source_collection]
            target_coll = self.target_client[target_db][target_collection]

            # 获取源集合统计信息
            source_stats = self.get_collection_stats(self.source_client, source_db, source_collection)
            if not source_stats or not source_stats['exists']:
                logger.error(f"源集合 {source_db}.{source_collection} 不存在")
                return False

            total_docs = source_stats['count']
            logger.info(f"源集合总文档数: {total_docs}")

            # 统计信息
            processed_count = 0
            updated_count = 0
            new_docs_count = 0
            skipped_count = 0

            logger.info("开始增量数据同步...")
            start_time = datetime.now()

            # 遍历源集合中的所有文档
            for source_doc in source_coll.find():
                processed_count += 1

                try:
                    # 在目标集合中查找对应文档
                    target_doc = target_coll.find_one({"_id": source_doc["_id"]})

                    if target_doc is None:
                        # 文档不存在，直接插入
                        target_coll.insert_one(source_doc)
                        new_docs_count += 1
                        logger.debug(f"新增文档: {source_doc['_id']}")
                    else:
                        # 文档存在，检查是否需要更新
                        if self.compare_document_dates(source_doc, target_doc):
                            # 获取需要更新的value数据
                            newer_values = self.get_newer_values(source_doc, target_doc)

                            if newer_values:
                                # 将新数据追加到目标文档的value数组中
                                target_coll.update_one(
                                    {"_id": source_doc["_id"]},
                                    {"$push": {"value": {"$each": newer_values}}}
                                )
                                updated_count += 1
                                logger.debug(f"更新文档: {source_doc['_id']}, 新增 {len(newer_values)} 条数据")
                            else:
                                skipped_count += 1
                        else:
                            skipped_count += 1

                except Exception as doc_error:
                    logger.error(f"处理文档 {source_doc.get('_id', 'unknown')} 失败: {str(doc_error)}")
                    continue

                # 显示进度
                if processed_count % batch_size == 0:
                    progress = (processed_count / total_docs) * 100
                    logger.info(f"已处理: {processed_count}/{total_docs} ({progress:.1f}%) - "
                              f"新增: {new_docs_count}, 更新: {updated_count}, 跳过: {skipped_count}")

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"增量数据同步完成!")
            logger.info(f"总处理文档数: {processed_count}")
            logger.info(f"新增文档数: {new_docs_count}")
            logger.info(f"更新文档数: {updated_count}")
            logger.info(f"跳过文档数: {skipped_count}")
            logger.info(f"耗时: {duration:.2f}秒")

            return True

        except Exception as e:
            logger.error(f"增量数据同步失败: {str(e)}")
            return False

    def sync_missing_dates_only(self, source_db: str, source_collection: str,
                               target_db: str, target_collection: str,
                               batch_size: int = 100) -> bool:
        """
        只同步目标文档中没有日期数据的文档

        Args:
            source_db: 源数据库名称
            source_collection: 源集合名称
            target_db: 目标数据库名称
            target_collection: 目标集合名称
            batch_size: 批处理大小

        Returns:
            bool: 同步成功返回True，否则返回False
        """
        try:
            # 获取源和目标集合
            source_coll = self.source_client[source_db][source_collection]
            target_coll = self.target_client[target_db][target_collection]

            logger.info("开始同步没有日期数据的文档...")
            start_time = datetime.now()

            # 查找目标集合中没有日期数据的文档
            # 查询条件：value数组为空或者value数组中没有date字段
            query = {
                "$or": [
                    {"value": {"$exists": False}},
                    {"value": {"$size": 0}},
                    {"value": {"$not": {"$elemMatch": {"date": {"$exists": True}}}}}
                ]
            }

            missing_date_docs = list(target_coll.find(query, {"_id": 1}))
            total_missing = len(missing_date_docs)

            logger.info(f"找到 {total_missing} 个没有日期数据的文档")

            if total_missing == 0:
                logger.info("所有文档都有日期数据，无需同步")
                return True

            updated_count = 0
            processed_count = 0

            # 遍历需要更新的文档
            for target_doc_ref in missing_date_docs:
                processed_count += 1
                doc_id = target_doc_ref["_id"]

                try:
                    # 从源集合获取对应文档
                    source_doc = source_coll.find_one({"_id": doc_id})

                    if source_doc and 'value' in source_doc and isinstance(source_doc['value'], list):
                        # 过滤出有日期的数据
                        dated_values = [v for v in source_doc['value'] if 'date' in v]

                        if dated_values:
                            # 更新目标文档
                            target_coll.update_one(
                                {"_id": doc_id},
                                {"$set": {"value": dated_values}}
                            )
                            updated_count += 1
                            logger.debug(f"更新文档: {doc_id}, 添加 {len(dated_values)} 条有日期的数据")

                except Exception as doc_error:
                    logger.error(f"处理文档 {doc_id} 失败: {str(doc_error)}")
                    continue

                # 显示进度
                if processed_count % batch_size == 0:
                    progress = (processed_count / total_missing) * 100
                    logger.info(f"已处理: {processed_count}/{total_missing} ({progress:.1f}%) - 更新: {updated_count}")

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"缺失日期数据同步完成!")
            logger.info(f"总处理文档数: {processed_count}")
            logger.info(f"成功更新文档数: {updated_count}")
            logger.info(f"耗时: {duration:.2f}秒")

            return True

        except Exception as e:
            logger.error(f"缺失日期数据同步失败: {str(e)}")
            return False

    def migrate_collection(self, source_db: str, source_collection: str,
                           target_db: str, target_collection: str,
                           batch_size: int = 1000, clear_target: bool = False) -> bool:
        """
        迁移集合数据

        Args:
            source_db: 源数据库名称
            source_collection: 源集合名称
            target_db: 目标数据库名称
            target_collection: 目标集合名称
            batch_size: 批处理大小
            clear_target: 是否清空目标集合

        Returns:
            bool: 迁移成功返回True，否则返回False
        """
        try:
            # 获取源和目标集合
            source_coll = self.source_client[source_db][source_collection]
            target_coll = self.target_client[target_db][target_collection]

            # 获取源集合统计信息
            source_stats = self.get_collection_stats(self.source_client, source_db, source_collection)
            if not source_stats or not source_stats['exists']:
                logger.error(f"源集合 {source_db}.{source_collection} 不存在")
                return False

            total_docs = source_stats['count']
            logger.info(f"源集合总文档数: {total_docs}")

            # 如果需要清空目标集合
            if clear_target:
                target_coll.delete_many({})
                logger.info("已清空目标集合")

            # 批量复制数据
            processed_count = 0
            batch_data = []

            logger.info("开始数据迁移...")
            start_time = datetime.now()

            for document in source_coll.find():
                batch_data.append(document)

                # 当批次数据达到指定大小时执行插入
                if len(batch_data) >= batch_size:
                    self._insert_batch(target_coll, batch_data)
                    processed_count += len(batch_data)
                    batch_data = []

                    # 显示进度
                    progress = (processed_count / total_docs) * 100
                    logger.info(f"已处理: {processed_count}/{total_docs} ({progress:.1f}%)")

            # 插入剩余数据
            if batch_data:
                self._insert_batch(target_coll, batch_data)
                processed_count += len(batch_data)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            logger.info(f"数据迁移完成!")
            logger.info(f"总处理文档数: {processed_count}")
            logger.info(f"耗时: {duration:.2f}秒")

            # 验证迁移结果
            target_stats = self.get_collection_stats(self.target_client, target_db, target_collection)
            if target_stats:
                logger.info(f"目标集合文档数: {target_stats['count']}")
                if target_stats['count'] == total_docs:
                    logger.info("✅ 数据迁移验证成功")
                    return True
                else:
                    logger.warning(f"⚠️ 数据数量不匹配，源: {total_docs}, 目标: {target_stats['count']}")

            return True

        except Exception as e:
            logger.error(f"数据迁移失败: {str(e)}")
            return False

    def _insert_batch(self, collection, batch_data):
        """
        批量插入数据

        Args:
            collection: 目标集合
            batch_data: 批次数据
        """
        try:
            if batch_data:
                collection.insert_many(batch_data, ordered=False)
        except Exception as e:
            logger.error(f"批量插入失败: {str(e)}")
            # 尝试逐个插入
            for doc in batch_data:
                try:
                    collection.insert_one(doc)
                except Exception as doc_error:
                    logger.error(f"单个文档插入失败: {str(doc_error)}")

    def close_connections(self):
        """关闭数据库连接"""
        if self.source_client:
            self.source_client.close()
            logger.info("已关闭源数据库连接")
        if self.target_client:
            self.target_client.close()
            logger.info("已关闭目标数据库连接")


def main():
    """主函数"""
    # 配置源和目标数据库
    source_config = {
        'host': '************',
        'port': 27017
    }

    target_config = {
        'host': 'localhost',
        'port': 27017
    }

    # 创建迁移器实例
    migrator = MongoDataMigrator(source_config, target_config)

    try:
        # 连接数据库
        if not migrator.connect():
            logger.error("无法连接到数据库，程序退出")
            return

        # 选择同步模式
        print("\n请选择同步模式:")
        print("1. 增量同步 - 只同步新的数据 (推荐)")
        print("2. 补充缺失日期 - 只更新没有日期数据的文档")
        print("3. 全量迁移 - 清空目标集合后重新导入所有数据")

        choice = input("请输入选择 (1-3): ").strip()

        success = False

        if choice == "1":
            # 增量同步模式
            logger.info("选择增量同步模式")
            success = migrator.incremental_sync_collection(
                source_db='Dashboard',
                source_collection='Product',
                target_db='Dashboard',
                target_collection='Product',
                batch_size=100
            )

        elif choice == "2":
            # 补充缺失日期模式
            logger.info("选择补充缺失日期模式")
            success = migrator.sync_missing_dates_only(
                source_db='Dashboard',
                source_collection='Product',
                target_db='Dashboard',
                target_collection='Product',
                batch_size=100
            )

        elif choice == "3":
            # 全量迁移模式
            logger.info("选择全量迁移模式")
            confirm = input("警告: 这将清空目标集合的所有数据! 确认继续? (y/N): ").strip().lower()
            if confirm == 'y':
                success = migrator.migrate_collection(
                    source_db='Dashboard',
                    source_collection='Product',
                    target_db='Dashboard',
                    target_collection='Product',
                    batch_size=1000,
                    clear_target=True
                )
            else:
                logger.info("用户取消操作")
                return
        else:
            logger.error("无效的选择，程序退出")
            return

        if success:
            logger.info("🎉 数据同步完成!")
        else:
            logger.error("❌ 数据同步失败!")

    except KeyboardInterrupt:
        logger.info("用户中断操作")
    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
    finally:
        # 关闭连接
        migrator.close_connections()


if __name__ == "__main__":
    main()