{
    "name": "对冲策略-均衡1.1手数",
    "tradelist": {
        "A": {
            "args": "[[90,10,0,200],[100,80,0,300],[100,70,0,300]]",
            "number" : NumberInt(50)
        },
        "AG": {
            "args": "[[90,10,0,200],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(15)
        },
        "AL": {
            "args": "[[70,30,0,200,-1],[60,40,0,200,-1],[70,30,0,400,-1]]",
            "number" : NumberInt(10)
        },
        "AO": {
            "args": "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(20)
        },
        "AP": {
            "args": "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(16)
        },
        "AU": {
            "args": "[[80,20,0,200],[20,0,0,200],[10,0,0,300],[80,20,0,300]]",
            "number" : NumberInt(4)
        },
        "B": {
            "args": "[[90,10,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(50)
        },
        "BR": {
            "args": "[[90,10,0,300],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(10)
        },
        "BU": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(16)
        },
        "C": {
            "args": "[[90,10,0,200],[80,20,0,200],[90,10,0,400]]",
            "number" : NumberInt(30)
        },
        "CF": {
            "args": "[[90,10,0,300],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(30)
        },
        "CJ": {
            "args": "[[90,10,0,400],[100,90,0,200],[100,80,0,200]]",
            "number" : NumberInt(16)
        },
        "CS": {
            "args": "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(50)
        },
        "CU": {
            "args": "[[80,20,0,200],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(5)
        },
        "EB": {
            "args": "[[90,10,0,300],[80,20,0,300],[80,20,0,400]]",
            "number" : NumberInt(16)
        },
        "EC": {
            "args": "[[90,10,0,300],[80,20,0,300],[80,20,0,400]]",
            "number" : NumberInt(6)
        },
        "EG": {
            "args": "[[90,10,0,100,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(16)
        },
        "FG": {
            "args": "[[90,10,0,300],[80,20,0,300],[90,10,0,400]]",
            "number" : NumberInt(50)
        },
        "FU": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(10)
        },
        "HC": {
            "args": "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(25)
        },
        "I": {
            "args": "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(20)
        },
        "J": {
            "args": "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(4)
        },
        "JD": {
            "args": "[[80,20,0,400],[100,90,0,300],[100,80,0,300]]",
            "number" : NumberInt(25)
        },
        "JM": {
            "args": "[[80,20,0,200],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(8)
        },
        "L": {
            "args": "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,200,-1]]",
            "number" : NumberInt(16)
        },
        "LC": {
            "args": "[[90,10,0,300],[80,20,0,300],[90,10,0,400]]",
            "number" : NumberInt(20)
        },
        "LH": {
            "args": "[[90,10,0,200],[80,20,0,200],[90,10,0,300]]",
            "number" : NumberInt(8)
        },
        "LU": {
            "args": "[[80,20,0,200,-1],[90,10,0,300,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(6)
        },
        "M": {
            "args": "[[90,10,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(50)
        },
        "MA": {
            "args": "[[90,10,0,200,-1],[90,10,0,300,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(35)
        },
        "NI": {
            "args": "[[80,20,0,100,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(8)
        },
        "OI": {
            "args": "[[90,10,0,200,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(16)
        },
        "P": {
            "args": "[[90,10,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(12)
        },
        "PB": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(12)
        },
        "PG": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(6)
        },
        "PK": {
            "args": "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,200,-1]]",
            "number" : NumberInt(16)
        },
        "PP": {
            "args": "[[80,20,0,300,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(16)
        },
        "PX": {
            "args": "[[80,20,0,300,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(7)
        },
        "RB": {
            "args": "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(25)
        },
        "RM": {
            "args": "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,200,-1]]",
            "number" : NumberInt(50)
        },
        "RU": {
            "args": "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(11)
        },
        "SA": {
            "args": "[[90,10,0,200],[90,10,0,300],[80,20,0,300]]",
            "number" : NumberInt(25)
        },
        "SC": {
            "args": "[[90,10,0,100],[80,20,0,100],[80,20,0,200]]",
            "number" : NumberInt(3)
        },
        "SF": {
            "args": "[[80,20,0,100],[90,10,0,200],[80,20,0,200]]",
            "number" : NumberInt(35)
        },
        "SH": {
            "args": "[[90,10,0,100],[80,20,0,100],[90,10,0,200]]",
            "number" : NumberInt(7)
        },
        "SI": {
            "args": "[[90,10,0,200],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(30)
        },
        "SM": {
            "args": "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(35)
        },
        "SN": {
            "args": "[[90,10,0,300],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(12)
        },
        "SP": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[90,10,0,400,-1]]",
            "number" : NumberInt(16)
        },
        "SR": {
            "args": "[[90,10,0,400,-1],[100,90,0,300,-1],[100,80,0,400,-1]]",
            "number" : NumberInt(16)
        },
        "T": {
            "args": "[[90,10,0,300],[80,20,0,300],[80,20,0,400]]",
            "number" : NumberInt(5)
        },
        "TA": {
            "args": "[[90,10,0,300,-1],[80,20,0,300,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(36)
        },
        "TF": {
            "args": "[[90,10,0,200],[90,10,0,300],[80,20,0,300]]",
            "number" : NumberInt(10)
        },
        "TL": {
            "args": "[[60,40,0,100],[90,10,0,300],[80,20,0,300]]",
            "number" : NumberInt(3)
        },
        "TS": {
            "args": "[[90,10,0,100],[90,10,0,200],[80,20,0,200]]",
            "number" : NumberInt(10)
        },
        "UR": {
            "args": "[[90,10,0,100],[90,10,0,200],[80,20,0,200]]",
            "number" : NumberInt(25)
        },
        "V": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[90,10,0,300,-1]]",
            "number" : NumberInt(25)
        },
        "Y": {
            "args": "[[80,20,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(12)
        },
        "ZN": {
            "args": "[[90,10,0,200,-1],[80,20,0,200,-1],[90,10,0,400,-1]]",
            "number" : NumberInt(15)
        }
    }
}