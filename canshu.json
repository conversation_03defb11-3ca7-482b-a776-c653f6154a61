{
    "_id" : ObjectId("68808694182ddf4cd14eac34"),
    "name" : "对冲策略",
    "detail" : "商品全是多敏感",
    "tradelist" : {
        "SC" : {
            "args" : "[[90,10,0,100],[80,20,0,100],[80,20,0,200]]",
            "number" : NumberInt(9)
        },
        "LU" : {
            "args" : "[[80,20,0,200,-1],[90,10,0,300,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(30)
        },
        "FU" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(36)
        },
        "PX" : {
            "args" : "[[80,20,0,300,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(12)
        },
        "TA" : {
            "args" : "[[90,10,0,300,-1],[80,20,0,300,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(24)
        },
        "EB" : {
            "args" : "[[90,10,0,300],[80,20,0,300],[80,20,0,400]]",
            "number" : NumberInt(27)
        },
        "BU" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(27)
        },
        "PG" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(12)
        },
        "JM" : {
            "args" : "[[80,20,0,200],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(30)
        },
        "J" : {
            "args" : "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(6)
        },
        "MA" : {
            "args" : "[[90,10,0,200,-1],[90,10,0,300,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(45)
        },
        "UR" : {
            "args" : "[[90,10,0,100],[90,10,0,200],[80,20,0,200]]",
            "number" : NumberInt(27)
        },
        "EG" : {
            "args" : "[[90,10,0,100,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(21)
        },
        "L" : {
            "args" : "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,200,-1]]",
            "number" : NumberInt(27)
        },
        "PP" : {
            "args" : "[[80,20,0,300,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(27)
        },
        "V" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[90,10,0,300,-1]]",
            "number" : NumberInt(39)
        },
        "SF" : {
            "args" : "[[80,20,0,100],[90,10,0,200],[80,20,0,200]]",
            "number" : NumberInt(18)
        },
        "SM" : {
            "args" : "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(18)
        },
        "SH" : {
            "args" : "[[90,10,0,100],[80,20,0,100],[90,10,0,200]]",
            "number" : NumberInt(12)
        },
        "AU" : {
            "args" : "[[80,20,0,200],[20,0,0,200],[10,0,0,300],[80,20,0,300]]",
            "number" : NumberInt(2)
        },
        "AG" : {
            "args" : "[[90,10,0,200],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(9)
        },
        "CU" : {
            "args" : "[[80,20,0,200],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(3)
        },
        "AL" : {
            "args" : "[[70,30,0,200,-1],[60,40,0,200,-1],[70,30,0,400,-1]]",
            "number" : NumberInt(9)
        },
        "ZN" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[90,10,0,400,-1]]",
            "number" : NumberInt(9)
        },
        "PB" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(12)
        },
        "NI" : {
            "args" : "[[80,20,0,100,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(6)
        },
        "SN" : {
            "args" : "[[90,10,0,300],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(3)
        },
        "AO" : {
            "args" : "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(18)
        },
        "LC" : {
            "args" : "[[90,10,0,300],[80,20,0,300],[90,10,0,400]]",
            "number" : NumberInt(15)
        },
        "SI" : {
            "args" : "[[90,10,0,200],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(27)
        },
        "RB" : {
            "args" : "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(18)
        },
        "HC" : {
            "args" : "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(18)
        },
        "I" : {
            "args" : "[[90,10,0,100],[80,20,0,100],[90,10,0,400]]",
            "number" : NumberInt(15)
        },
        "FG" : {
            "args" : "[[90,10,0,300],[80,20,0,300],[90,10,0,400]]",
            "number" : NumberInt(48)
        },
        "SA" : {
            "args" : "[[90,10,0,200],[90,10,0,300],[80,20,0,300]]",
            "number" : NumberInt(39)
        },
        "RU" : {
            "args" : "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,300,-1]]",
            "number" : NumberInt(6)
        },
        "BR" : {
            "args" : "[[90,10,0,300],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(15)
        },
        "SP" : {
            "args" : "[[90,10,0,200,-1],[80,20,0,200,-1],[90,10,0,400,-1]]",
            "number" : NumberInt(18)
        },
        "CF" : {
            "args" : "[[90,10,0,300],[90,10,0,400],[80,20,0,400]]",
            "number" : NumberInt(15)
        },
        "A" : {
            "args" : "[[90,10,0,200],[100,80,0,300],[100,70,0,300]]",
            "number" : NumberInt(27)
        },
        "B" : {
            "args" : "[[90,10,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(15)
        },
        "M" : {
            "args" : "[[90,10,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(24)
        },
        "RM" : {
            "args" : "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,200,-1]]",
            "number" : NumberInt(48)
        },
        "Y" : {
            "args" : "[[80,20,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(15)
        },
        "OI" : {
            "args" : "[[90,10,0,200,-1],[90,10,0,400,-1],[80,20,0,400,-1]]",
            "number" : NumberInt(6)
        },
        "P" : {
            "args" : "[[90,10,0,300],[20,0,0,200],[10,0,0,300]]",
            "number" : NumberInt(15)
        },
        "PK" : {
            "args" : "[[90,10,0,100,-1],[80,20,0,100,-1],[80,20,0,200,-1]]",
            "number" : NumberInt(15)
        },
        "C" : {
            "args" : "[[90,10,0,200],[80,20,0,200],[90,10,0,400]]",
            "number" : NumberInt(48)
        },
        "CS" : {
            "args" : "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(18)
        },
        "SR" : {
            "args" : "[[90,10,0,400,-1],[100,90,0,300,-1],[100,80,0,400,-1]]",
            "number" : NumberInt(15)
        },
        "AP" : {
            "args" : "[[90,10,0,200],[80,20,0,200],[80,20,0,300]]",
            "number" : NumberInt(12)
        },
        "CJ" : {
            "args" : "[[90,10,0,400],[100,90,0,200],[100,80,0,200]]",
            "number" : NumberInt(24)
        },
        "LH" : {
            "args" : "[[90,10,0,200],[80,20,0,200],[90,10,0,300]]",
            "number" : NumberInt(6)
        },
        "JD" : {
            "args" : "[[80,20,0,400],[100,90,0,300],[100,80,0,300]]",
            "number" : NumberInt(30)
        },
        "EC" : {
            "args" : "[[90,10,0,300],[80,20,0,300],[80,20,0,400]]",
            "number" : NumberInt(12)
        },
        "TS" : {
            "args" : "[[90,10,0,100],[90,10,0,200],[80,20,0,200]]",
            "number" : NumberInt(30)
        },
        "TF" : {
            "args" : "[[90,10,0,200],[90,10,0,300],[80,20,0,300]]",
            "number" : NumberInt(30)
        },
        "T" : {
            "args" : "[[90,10,0,300],[80,20,0,300],[80,20,0,400]]",
            "number" : NumberInt(18)
        },
        "TL" : {
            "args" : "[[60,40,0,100],[90,10,0,300],[80,20,0,300]]",
            "number" : NumberInt(9)
        }
    }
}