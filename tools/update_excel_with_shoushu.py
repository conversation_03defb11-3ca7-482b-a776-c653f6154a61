import pandas as pd
import json
import re

# 1. 读取全商品.xlsx
excel_path = '全商品.xlsx'
df = pd.read_excel(excel_path, engine='openpyxl')

# 2. 读取shoushu.json并处理MongoDB特殊语法
with open('shoushu.json', 'r', encoding='utf-8') as f:
    content = f.read()
content = re.sub(r'ObjectId\("([^"]+)"\)', r'"\1"', content)
content = re.sub(r'NumberInt\((\d+)\)', r'\1', content)
data = json.loads(content)
tradelist = data.get('tradelist', {})

# 3. 构建商品名->手数字典
shoushu_map = {k: v.get('number') for k, v in tradelist.items() if v.get('number') is not None}

# 4. 只保留“商品”和“手数”两列（假设表头为“商品”“手数”，否则需调整列名）
if '商品' in df.columns and '手数' in df.columns:
    # 5. 更新手数
    df['手数'] = df['商品'].map(shoushu_map).fillna(df['手数'])
    # 6. 保存结果
    df[['商品', '手数']].to_excel('全商品_手数已更新.xlsx', index=False, engine='openpyxl')
    print('已生成全商品_手数已更新.xlsx')
else:
    print('未找到“商品”或“手数”列，请检查Excel表头！') 