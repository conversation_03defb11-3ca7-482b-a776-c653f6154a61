import pandas as pd
import json
import re

# 读取Excel文件
excel_path = '全商品.xlsx'  # 路径已修正为当前目录
sheet_name = 0  # 默认第一个sheet

def parse_param(param_str, weight):
    """
    将参数字符串如 '100-90-10' 转换为 [90, 10, 0, 100] 或 [90, 10, 0, 100, -1] 格式。
    """
    if not isinstance(param_str, str):
        return None
    nums = re.findall(r'\d+', param_str)
    if not nums:
        return None
    nums = [int(n) for n in nums]
    if len(nums) == 3:
        arr = [nums[1], nums[2], 0, nums[0]]
    elif len(nums) == 2:
        arr = [nums[1], 0, 0, nums[0]]
    elif len(nums) == 4:
        arr = [nums[1], nums[2], nums[3], nums[0]]
    else:
        while len(nums) < 4:
            nums.append(0)
        arr = [nums[1] if len(nums)>1 else 0, nums[2] if len(nums)>2 else 0, nums[3] if len(nums)>3 else 0, nums[0]]
    if weight == -1:
        arr.append(-1)
    return arr

def main():
    df = pd.read_excel(excel_path, sheet_name=sheet_name, header=None)
    # G=6, C=2, D=3, E=4, F=5, H=7
    result = {}
    for idx, row in df.iterrows():
        name = row[6]
        number = row[2]
        params = [row[3], row[4], row[5]]
        try:
            weight = int(row[7])
        except:
            weight = 1
        param_list = []
        for p in params:
            parsed = parse_param(p, weight)
            if parsed:
                param_list.append(parsed)
        if not param_list:
            continue
        if name not in result:
            result[name] = {"args": [], "number": 0, "weight": weight}
        result[name]["args"].extend(param_list)
        # 手数累加
        try:
            n = int(number)
        except:
            n = 0
        result[name]["number"] += n
        result[name]["weight"] = weight  # 记录权重，后面判断
    # 生成JSON
    output = {}
    for k, v in result.items():
        args = v["args"]
        # 如果权重为-1，每个参数都加-1（已在parse_param处理）
        output[k] = {
            "args": json.dumps(args, ensure_ascii=False),
            "number": f"NumberInt({v['number']})"
        }
    # 输出到文件
    with open('output.json', 'w', encoding='utf-8') as f:
        json.dump(output, f, ensure_ascii=False, indent=4)
    print('已生成 output.json')

def update_numbers():
    # 读取手数文件
    df_hands = pd.read_csv('手数', sep='\t', header=None, names=['product', 'number'])
    hands = dict(zip(df_hands['product'], df_hands['number'].astype(int)))
    
    # 读取 output.json 作为文本
    with open('output.json', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 对于每个在 hands 中的商品，替换其 number
    for prod, num in hands.items():
        # 构建 regex：寻找特定商品的 number 行
        # 假设格式为 "商品" : { "args" : "...", "number" : NumberInt(old) }
        pattern = rf'("{prod}"[ \t]*:[ \t]*{{[ \t]*"args"[ \t]*:[ \t]*"[^"]+",[ \t]*"number"[ \t]*:[ \t]*NumberInt\(\d+\)[ \t]*}})' 
        replacement = lambda m: re.sub(r'NumberInt\(\d+\)', f'NumberInt({num})', m.group(0))
        content = re.sub(pattern, replacement, content)
    
    # 写回 output.json
    with open('output.json', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print('已更新 output.json 中的手数值')

if __name__ == '__main__':
    # main()  # 注释掉原 main，如果需要生成可以取消注释
    update_numbers() 