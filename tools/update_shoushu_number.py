import json
import re
import shutil
import os

# 1. 读取“手数”文件，生成商品名到手数字典
def read_shoushu_txt(filepath):
    shoushu_dict = {}
    with open(filepath, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue
            parts = line.split('\t') if '\t' in line else line.split()
            if len(parts) == 2:
                name, num = parts
                try:
                    shoushu_dict[name] = int(num)
                except ValueError:
                    continue
    return shoushu_dict

# 2. 读取 shoushu.json，处理 ObjectId/NumberInt 为标准 JSON
def load_json_with_fix(filepath):
    with open(filepath, 'r', encoding='utf-8') as f:
        text = f.read()
    # 替换 ObjectId("xxx") 为 "xxx"
    text = re.sub(r'ObjectId\(["\"](.*?)["\"]\)', r'"\1"', text)
    # 替换 NumberInt(xxx) 为 xxx
    text = re.sub(r'NumberInt\((\d+)\)', r'\1', text)
    # 替换单引号为双引号（如有）
    text = text.replace("'", '"')
    # 去除多余逗号
    text = re.sub(r',\s*}', '}', text)
    text = re.sub(r',\s*]', ']', text)
    data = json.loads(text)
    return data

# 3. 遍历 tradelist，若商品名在手数字典中，则更新 number 字段
def update_tradelist_numbers(data, shoushu_dict):
    tradelist = data.get('tradelist', {})
    for name, info in tradelist.items():
        if name in shoushu_dict:
            info['number'] = shoushu_dict[name]
    return data

# 4. 写回 shoushu.json（标准 JSON 格式），并备份原文件
def save_json(filepath, data):
    # 先备份原文件
    shutil.copy(filepath, filepath + '.bak')
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=4)

if __name__ == '__main__':
    # 自动获取项目根目录
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    shoushu_txt_path = os.path.join(BASE_DIR, '手数')
    shoushu_json_path = os.path.join(BASE_DIR, 'shoushu.json')

    shoushu_dict = read_shoushu_txt(shoushu_txt_path)
    data = load_json_with_fix(shoushu_json_path)
    data = update_tradelist_numbers(data, shoushu_dict)
    save_json(shoushu_json_path, data)
    print('shoushu.json 已根据“手数”文件成功更新，并已备份原文件。') 