import json
import pandas as pd
import re

# 读取JSON文件
with open('shoushu.json', 'r', encoding='utf-8') as f:
    content = f.read()

# 处理MongoDB特定语法：替换ObjectId和NumberInt
content = re.sub(r'ObjectId\("([^"]+)"\)', r'"\1"', content)
content = re.sub(r'NumberInt\((\d+)\)', r'\1', content)

# 解析JSON
data = json.loads(content)

# 提取tradelist
tradelist = data.get('tradelist', {})

# 准备数据
extracted_data = []
for product, info in tradelist.items():
    number = info.get('number')
    if number is not None:
        extracted_data.append({'商品名': product, '手数': number})

# 创建DataFrame
df = pd.DataFrame(extracted_data)

# 保存到Excel
df.to_excel('shoushu_extract.xlsx', index=False, engine='openpyxl')

print("Excel文件已生成：shoushu_extract.xlsx") 