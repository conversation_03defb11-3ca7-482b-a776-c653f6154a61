import pandas as pd
import json
import re

# 1. 读取全商品.xlsx
excel_path = '全商品.xlsx'
df = pd.read_excel(excel_path, engine='openpyxl')

# 2. 构建商品名->手数字典
if '商品' not in df.columns or '手数' not in df.columns:
    raise ValueError('未找到“商品”或“手数”列，请检查Excel表头！')
excel_map = dict(zip(df['商品'], df['手数']))

# 3. 读取shoushu.json并处理MongoDB特殊语法
with open('shoushu.json', 'r', encoding='utf-8') as f:
    content = f.read()
content = re.sub(r'ObjectId\("([^"]+)"\)', r'"\1"', content)
content = re.sub(r'NumberInt\((\d+)\)', r'\1', content)
data = json.loads(content)
tradelist = data.get('tradelist', {})

# 4. 批量更新number
for k, v in tradelist.items():
    if k in excel_map and pd.notna(excel_map[k]):
        v['number'] = int(excel_map[k])

# 5. 恢复MongoDB格式的NumberInt
for k, v in tradelist.items():
    if isinstance(v.get('number'), int):
        v['number'] = f'NumberInt({v["number"]})'

# 6. 写回文件
with open('shoushu_手数已更新.json', 'w', encoding='utf-8') as f:
    json.dump(data, f, ensure_ascii=False, indent=4)

print('已生成 shoushu_手数已更新.json')